/* Cart item animations */
.cart-item {
  transition: all 0.3s ease;
}

.cart-item:hover {
  background-color: #f9fafb;
  transform: translateY(-1px);
}

/* Quantity controls */
.quantity-btn {
  @apply w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 transition-colors;
}

.quantity-btn:hover {
  border-color: #c1440e;
  color: #c1440e;
}

/* Form styles */
.form-section {
  background-color: #f9fafb;
  padding: 1.5rem;
  border-radius: 0.75rem;
  margin-bottom: 1.5rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  ring: 2px solid #c1440e;
  border-color: transparent;
}

.form-error {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
}

.btn-primary {
  background-color: #c1440e;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #a03a0c;
  transform: translateY(-1px);
}

.btn-primary:disabled {
  background-color: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: transparent;
  color: #c1440e;
  border: 2px solid #c1440e;
}

.btn-secondary:hover {
  background-color: #fef3c7;
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

/* Cart summary styles */
.cart-summary {
  background-color: #f9fafb;
  padding: 1.5rem;
  border-radius: 0.75rem;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.summary-total {
  font-size: 1.125rem;
  font-weight: 700;
  padding-top: 0.5rem;
  border-top: 1px solid #d1d5db;
}

/* Empty cart styles */
.empty-cart {
  text-align: center;
  padding: 4rem 1rem;
}

.empty-cart-icon {
  width: 6rem;
  height: 6rem;
  margin: 0 auto 1rem;
  color: #9ca3af;
}

/* Success message styles */
.success-container {
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  text-align: center;
  max-width: 32rem;
  margin: 0 auto;
}

.success-icon {
  width: 4rem;
  height: 4rem;
  background-color: #dcfce7;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .cart-item-grid {
    flex-direction: column;
    gap: 1rem;
  }
  
  .cart-item-image {
    width: 100%;
    max-width: 200px;
    margin: 0 auto;
  }
  
  .quantity-controls {
    justify-content: center;
  }
  
  .item-total {
    text-align: center;
  }
  
  .btn-group {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .btn-group .btn {
    width: 100%;
  }
}

/* Animation classes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #c1440e;
  border-radius: 50%;
  width: 1rem;
  height: 1rem;
  animation: spin 1s linear infinite;
  display: inline-block;
  margin-right: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Checkout form specific styles */
.checkout-form {
  max-width: 42rem;
  margin: 0 auto;
}

.checkout-section {
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
}

.checkout-header {
  text-align: center;
  margin-bottom: 2rem;
}

.order-summary {
  background-color: #fef3c7;
  padding: 1.5rem;
  border-radius: 0.75rem;
  border: 1px solid #fbbf24;
}

/* Custom checkbox styles */
.custom-checkbox {
  appearance: none;
  width: 1rem;
  height: 1rem;
  border: 2px solid #d1d5db;
  border-radius: 0.25rem;
  background-color: white;
  cursor: pointer;
  position: relative;
}

.custom-checkbox:checked {
  background-color: #c1440e;
  border-color: #c1440e;
}

.custom-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 0.75rem;
  font-weight: bold;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}
