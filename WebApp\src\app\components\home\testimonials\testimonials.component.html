<section class="py-16 md:py-24 bg-gray-50">
  <div class="container mx-auto px-4">
    <!-- Section Title -->
    <div class="text-center mb-12" @fadeIn>
      <h2 class="text-3xl md:text-4xl font-heading font-semibold text-primary-500 mb-4">Guest Experiences</h2>
      <p class="text-lg text-gray-600 max-w-3xl mx-auto">
        Hear what our guests have to say about their stay at Mithilani Ghar.
      </p>
    </div>
    
    <!-- Testimonial Slider -->
    <div class="max-w-4xl mx-auto relative" 
         (mouseenter)="pauseSlider()" 
         (mouseleave)="resumeSlider()">
      
      <!-- Testimonials -->
      <div class="bg-white rounded-xl shadow-xl overflow-hidden">
        <div *ngFor="let testimonial of testimonials; let i = index" 
             class="p-8 md:p-12"
             [class.hidden]="i !== currentSlide"
             @fadeSlide>
          
          <!-- Quote Icon -->
          <div class="text-primary-200 mb-6">
            <svg class="w-12 h-12" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
              <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z"/>
            </svg>
          </div>
          
          <!-- Quote Text -->
          <blockquote class="text-xl md:text-2xl text-gray-700 italic mb-8 leading-relaxed">
            "{{testimonial.quote}}"
          </blockquote>
          
          <!-- Author Info -->
          <div class="flex items-center">
            <img [src]="testimonial.image" [alt]="testimonial.name" class="w-16 h-16 rounded-full object-cover border-2 border-primary-500">
            <div class="ml-4">
              <div class="font-semibold text-gray-900">{{testimonial.name}}</div>
              <div class="text-gray-500">{{testimonial.location}}</div>
              
              <!-- Star Rating -->
              <div class="flex mt-1">
                <svg *ngFor="let star of [1,2,3,4,5]" 
                     xmlns="http://www.w3.org/2000/svg" 
                     class="h-5 w-5" 
                     [class.text-secondary-500]="star <= testimonial.rating"
                     [class.text-gray-300]="star > testimonial.rating"
                     viewBox="0 0 20 20" 
                     fill="currentColor">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Navigation Dots -->
      <div class="flex justify-center mt-6 space-x-2">
        <button *ngFor="let testimonial of testimonials; let i = index" 
                (click)="setCurrentSlide(i)"
                class="w-3 h-3 rounded-full transition-colors duration-300"
                [class.bg-primary-500]="i === currentSlide"
                [class.bg-gray-300]="i !== currentSlide">
        </button>
      </div>
    </div>
  </div>
</section>
