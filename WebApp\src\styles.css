/* You can add global styles to this file, and also import other style files */
/* Custom Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Merriweather:wght@400;700&family=Playfair+Display:wght@400;500;600;700&family=Cinzel:wght@400;500;600;700&family=Open+Sans:wght@300;400;500;600;700&family=Lato:wght@300;400;700&family=Caveat:wght@400;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles */
@layer base {
  body {
    @apply font-body text-background-dark bg-background-light overflow-x-hidden;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading font-semibold;
  }
  h1 {
    @apply text-3xl sm:text-4xl md:text-5xl lg:text-6xl;
  }
  h2 {
    @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl;
  }
  h3 {
    @apply text-xl sm:text-2xl md:text-3xl;
  }
  h4 {
    @apply text-lg sm:text-xl md:text-2xl;
  }
  h5 {
    @apply text-base sm:text-lg md:text-xl;
  }
  h6 {
    @apply text-sm sm:text-base md:text-lg;
  }
  a {
    @apply text-primary-500 hover:text-primary-600 transition-colors duration-300;
  }

  /* Responsive container padding */
  .container {
    @apply px-4 sm:px-6 lg:px-8;
  }
}

@layer components {
  .btn {
    @apply px-5 py-3 rounded-md font-medium transition-all duration-300 transform hover:-translate-y-1 hover:shadow-md;
  }
  .btn-primary {
    @apply bg-primary-500 text-white hover:bg-primary-600;
  }
  .btn-secondary {
    @apply bg-secondary-500 text-background-dark hover:bg-secondary-600;
  }
  .btn-accent {
    @apply bg-accent-500 text-white hover:bg-accent-600;
  }
  .btn-success {
    @apply bg-success-500 text-white hover:bg-success-600;
  }
  .btn-outline {
    @apply border-2 border-primary-500 text-primary-500 hover:bg-primary-50;
  }
  .btn-outline-secondary {
    @apply border-2 border-secondary-500 text-secondary-600 hover:bg-secondary-50;
  }
  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300;
  }
  .input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }
  .section {
    @apply py-16 md:py-20 lg:py-24;
  }
  .section-alt {
    @apply py-16 md:py-20 lg:py-24 bg-primary-50;
  }
  .container {
    @apply mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl;
  }
  .heading-decorated {
    @apply relative inline-block;
  }
  .heading-decorated::after {
    @apply content-[''] absolute -bottom-2 left-0 w-1/3 h-1 bg-primary-500;
  }
  .mithila-card {
    @apply relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1;
  }
}

/* Mithila Art Specific Styles */
.mithila-border {
  @apply border-4 border-mithila-red p-1 relative;
  background-image: repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(244, 180, 0, 0.15) 10px, rgba(244, 180, 0, 0.15) 20px);
}

.mithila-border::before {
  @apply content-[''] absolute -top-2 -left-2 w-4 h-4 border-t-4 border-l-4 border-mithila-blue;
}

.mithila-border::after {
  @apply content-[''] absolute -bottom-2 -right-2 w-4 h-4 border-b-4 border-r-4 border-mithila-blue;
}

.mithila-card {
  @apply card p-6 border-t-4 border-mithila-red;
}

.mithila-heading {
  @apply font-display text-3xl md:text-4xl lg:text-5xl text-mithila-red leading-tight;
}

.mithila-pattern-bg {
  background-color: #FAF8F1;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23C1440E' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.mithila-divider {
  @apply relative h-4 my-8;
}

.mithila-divider::before {
  @apply content-[''] absolute top-1/2 left-0 w-full h-0.5 bg-mithila-red bg-opacity-20;
}

.mithila-divider::after {
  @apply content-[''] absolute top-1/2 left-1/2 w-16 h-0.5 bg-mithila-red -translate-x-1/2 -translate-y-1/2;
}

/* Beautiful Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes floatSlow {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes floatMedium {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes floatFast {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-25px);
  }
}

@keyframes spinSlow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Animation Classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-slow {
  animation: floatSlow 8s ease-in-out infinite;
}

.animate-float-medium {
  animation: floatMedium 7s ease-in-out infinite;
}

.animate-float-fast {
  animation: floatFast 5s ease-in-out infinite;
}

.animate-spin-slow {
  animation: spinSlow 20s linear infinite;
}

.animate-pulse-slow {
  animation: pulse 3s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

/* Animation Delays */
.animation-delay-300 {
  animation-delay: 300ms;
}

.animation-delay-600 {
  animation-delay: 600ms;
}

.animation-delay-900 {
  animation-delay: 900ms;
}

/* Hover Effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hover-glow {
  transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(193, 68, 14, 0.3);
}

/* Interactive Elements */
.interactive-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.interactive-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Gradient Animations */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradientShift 4s ease infinite;
}

/* Enhanced Floating Design System Animations */
@keyframes gradient-background {
  0%, 100% {
    background-position: 0% 50%;
    opacity: 0.7;
  }
  50% {
    background-position: 100% 50%;
    opacity: 0.9;
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 0%;
    opacity: 0.3;
  }
  50% {
    background-position: 100% 100%;
    opacity: 0.6;
  }
}

@keyframes ping-slow {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes border-glow {
  0%, 100% {
    opacity: 0.5;
    filter: drop-shadow(0 0 5px rgba(255,255,255,0.3));
  }
  50% {
    opacity: 0.8;
    filter: drop-shadow(0 0 15px rgba(255,255,255,0.5));
  }
}

@keyframes fade-in-delay-1 {
  0% { opacity: 0; transform: translateY(20px); }
  100% { opacity: 1; transform: translateY(0); }
}

@keyframes fade-slide-in {
  0% { opacity: 0; transform: translateY(30px); }
  100% { opacity: 1; transform: translateY(0); }
}

@keyframes fade-slide-in-left {
  0% { opacity: 0; transform: translateX(-30px); }
  100% { opacity: 1; transform: translateX(0); }
}

@keyframes fade-slide-in-right {
  0% { opacity: 0; transform: translateX(30px); }
  100% { opacity: 1; transform: translateX(0); }
}

@keyframes stagger-in {
  0% { opacity: 0; transform: translateY(20px); }
  100% { opacity: 1; transform: translateY(0); }
}

/* Enhanced Animation Classes */
.animate-gradient-background {
  animation: gradient-background 8s ease-in-out infinite;
  background-size: 200% 200%;
}

.animate-gradient-shift {
  animation: gradient-shift 12s ease-in-out infinite;
  background-size: 200% 200%;
}

.animate-ping-slow {
  animation: ping-slow 3s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.animate-border-glow {
  animation: border-glow 5s ease-in-out infinite;
}

.animate-fade-in-delay-1 {
  animation: fade-in-delay-1 1s ease-out 1s both;
}

.animate-fade-slide-in {
  animation: fade-slide-in 0.8s ease-out;
}

.animate-fade-slide-in-left {
  animation: fade-slide-in-left 0.8s ease-out;
}

.animate-fade-slide-in-right {
  animation: fade-slide-in-right 0.8s ease-out;
}

.animate-stagger-in {
  animation: stagger-in 0.6s ease-out;
}

/* Gradient Utilities */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

/* Responsive Floating Elements */
@media (max-width: 768px) {
  .mithila-floating-section .absolute {
    transform: scale(0.7);
  }

  .animate-float-slow,
  .animate-float-medium,
  .animate-float-fast {
    animation-duration: 4s;
  }
}

/* Enhanced Interactive Effects */
.mithila-section-hover:hover .animate-float-slow {
  animation-duration: 4s;
}

.mithila-section-hover:hover .animate-float-medium {
  animation-duration: 3s;
}

.mithila-section-hover:hover .animate-float-fast {
  animation-duration: 2s;
}

/* Backdrop Blur Utilities */
.backdrop-blur-xs {
  backdrop-filter: blur(1px);
}

.backdrop-blur-sm {
  backdrop-filter: blur(2px);
}

/* Enhanced Card Styles for Floating Design */
.mithila-floating-card {
  @apply relative overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.mithila-floating-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(193, 68, 14, 0.05), rgba(244, 180, 0, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.mithila-floating-card:hover::before {
  opacity: 1;
}

/* Enhanced Responsive Design System */

/* Mobile First Approach - Base styles for mobile */
@media (max-width: 640px) {
  /* Typography adjustments for mobile */
  h1 {
    @apply text-2xl sm:text-3xl;
    line-height: 1.2;
  }

  h2 {
    @apply text-xl sm:text-2xl;
    line-height: 1.3;
  }

  h3 {
    @apply text-lg sm:text-xl;
    line-height: 1.4;
  }

  /* Container adjustments */
  .container {
    @apply px-3 sm:px-4;
  }

  /* Section padding adjustments */
  .section {
    @apply py-8 sm:py-12;
  }

  /* Card adjustments */
  .card {
    @apply rounded-lg;
    margin-bottom: 1rem;
  }

  /* Button adjustments */
  .btn {
    @apply px-4 py-2 text-sm;
  }

  /* Input adjustments */
  .input {
    @apply px-3 py-2 text-sm;
  }

  /* Grid adjustments */
  .grid-responsive {
    @apply grid-cols-1 gap-4;
  }

  /* Floating elements scale down */
  .mithila-floating-card {
    @apply rounded-lg shadow-md;
    transform: scale(0.95);
  }

  /* Animation adjustments for mobile */
  .animate-float,
  .animate-float-slow,
  .animate-float-medium,
  .animate-float-fast {
    animation-duration: 3s;
  }

  /* Reduce motion for mobile performance */
  .interactive-card:hover {
    transform: translateY(-4px) scale(1.01);
  }
}

/* Tablet styles */
@media (min-width: 641px) and (max-width: 1024px) {
  /* Typography for tablet */
  h1 {
    @apply text-3xl md:text-4xl;
  }

  h2 {
    @apply text-2xl md:text-3xl;
  }

  /* Container for tablet */
  .container {
    @apply px-4 md:px-6;
  }

  /* Section padding for tablet */
  .section {
    @apply py-12 md:py-16;
  }

  /* Grid for tablet */
  .grid-responsive {
    @apply grid-cols-2 gap-6;
  }

  /* Cards for tablet */
  .mithila-floating-card {
    @apply rounded-xl;
    transform: scale(0.98);
  }
}

/* Desktop styles */
@media (min-width: 1025px) {
  /* Typography for desktop */
  h1 {
    @apply text-4xl lg:text-5xl xl:text-6xl;
  }

  h2 {
    @apply text-3xl lg:text-4xl xl:text-5xl;
  }

  /* Container for desktop */
  .container {
    @apply px-6 lg:px-8;
  }

  /* Section padding for desktop */
  .section {
    @apply py-16 lg:py-20 xl:py-24;
  }

  /* Grid for desktop */
  .grid-responsive {
    @apply grid-cols-3 lg:grid-cols-4 gap-8;
  }

  /* Enhanced hover effects for desktop */
  .mithila-floating-card:hover {
    transform: translateY(-8px) scale(1.02);
  }

  .interactive-card:hover {
    transform: translateY(-12px) scale(1.03);
  }
}

/* Large desktop styles */
@media (min-width: 1440px) {
  .container {
    @apply max-w-7xl;
  }

  .section {
    @apply py-20 lg:py-24 xl:py-28;
  }

  /* Enhanced spacing for large screens */
  .grid-responsive {
    @apply gap-10;
  }
}

/* Ultra-wide screen styles */
@media (min-width: 1920px) {
  .container {
    max-width: 96rem; /* 1536px equivalent to max-w-8xl */
  }

  /* Prevent content from becoming too wide */
  .content-max-width {
    max-width: 1600px;
    margin: 0 auto;
  }
}

/* Responsive utility classes */
.responsive-grid-1-2-3 {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8;
}

.responsive-grid-1-2-4 {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 lg:gap-8;
}

.responsive-flex-col-row {
  @apply flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-8;
}

.responsive-text-center-left {
  @apply text-center md:text-left;
}

.responsive-padding {
  @apply p-4 md:p-6 lg:p-8;
}

.responsive-margin {
  @apply m-4 md:m-6 lg:m-8;
}

/* Touch-friendly improvements for mobile */
@media (max-width: 768px) {
  /* Larger touch targets */
  button, .btn, a[role="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better spacing for touch */
  .touch-spacing > * + * {
    margin-top: 0.75rem;
  }

  /* Improved form elements */
  input, textarea, select {
    min-height: 44px;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Better modal sizing */
  .modal-content {
    @apply mx-4 my-8 max-h-screen overflow-y-auto;
  }
}

/* Print styles */
@media print {
  /* Hide interactive elements */
  .no-print,
  button,
  .btn,
  nav,
  .navigation,
  .floating-elements {
    display: none !important;
  }

  /* Optimize for print */
  .container {
    max-width: none !important;
    padding: 0 !important;
  }

  .section {
    padding: 1rem 0 !important;
  }

  /* Ensure good contrast */
  * {
    color: #000 !important;
    background: #fff !important;
  }

  /* Page breaks */
  .page-break {
    page-break-before: always;
  }

  .avoid-break {
    page-break-inside: avoid;
  }
}
