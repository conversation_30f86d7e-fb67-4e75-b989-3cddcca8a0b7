import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { MithilaSectionComponent } from '../../components/shared/mithila-section/mithila-section.component';
import { SectionTitleComponent } from '../../components/shared/section-title/section-title.component';

@Component({
  selector: 'app-terms-of-service',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    MithilaSectionComponent,
    SectionTitleComponent
  ],
  templateUrl: './terms-of-service.component.html',
  styleUrl: './terms-of-service.component.css'
})
export class TermsOfServiceComponent {
  lastUpdated = 'December 21, 2024';
}
