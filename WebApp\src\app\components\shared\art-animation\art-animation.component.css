.art-animation-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  z-index: 5;
}

.art-element {
  position: absolute;
  opacity: 0;
  animation: float 15s ease-in-out infinite;
}

@keyframes float {
  0% {
    opacity: 0;
    transform: translateY(20px) rotate(0deg);
  }
  10% {
    opacity: 0.7;
  }
  90% {
    opacity: 0.7;
  }
  100% {
    opacity: 0;
    transform: translateY(-20px) rotate(360deg);
  }
}

/* <PERSON><PERSON><PERSON>pes */
.circle {
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.7);
}

.square {
  border: 1px solid rgba(255, 255, 255, 0.7);
}

.triangle {
  width: 0 !important;
  height: 0 !important;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 20px solid rgba(255, 255, 255, 0.7);
}

.line {
  height: 1px !important;
  background-color: rgba(255, 255, 255, 0.7);
}

.dot {
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.7);
  width: 5px !important;
  height: 5px !important;
}

.fish {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='1' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M18 10c3.5 0 6 2.5 6 5s-2.5 5-6 5c-1.5 0-3-.5-4-1.5C12 20 9 22 4 22c0-5 2-8 4-9.5C7 11.5 5.5 10 4 10c5 0 8 2 9.5 4 1-1.5 3-4 4.5-4z'/%3E%3Ccircle cx='16' cy='13' r='1'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.flower {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='1' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='3'/%3E%3Cpath d='M12 2v3M12 19v3M2 12h3M19 12h3M4.93 4.93l2.12 2.12M16.95 16.95l2.12 2.12M4.93 19.07l2.12-2.12M16.95 7.05l2.12-2.12'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.bird {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='1' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M18 6L4 20M10 6h8v8M6 14l8-8'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.leaf {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='1' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M2 22l10-10M16 2c-1.5 2-2 4.5-2 7 0 3.5 1 6.5 3 8.5 1.5 1.5 3.5 2.5 5 3 0-1.5 0-3.5-1-5-1.5-2-4-3-7-3-2.5 0-5 .5-7 2'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
