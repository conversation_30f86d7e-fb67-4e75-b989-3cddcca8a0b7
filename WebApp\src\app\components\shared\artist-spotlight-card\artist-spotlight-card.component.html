<!-- Artist Spotlight Section -->
<div class="flex flex-col lg:flex-row items-center lg:items-start gap-8 lg:gap-12">

  <!-- Artist Image -->
  <div class="flex-shrink-0 w-full lg:w-1/3">
    <div class="relative overflow-hidden rounded-2xl shadow-lg">
      <img [src]="artist.image" [alt]="artist.name"
           class="w-full h-80 lg:h-96 object-cover transition-transform duration-500 hover:scale-105">

      <!-- Subtle Gradient Overlay -->
      <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
    </div>
  </div>

  <!-- Artist Content -->
  <div class="flex-1 w-full lg:w-2/3">
    <div class="mb-6">
      <h3 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-3">{{artist.name}}</h3>
      <p class="text-primary-600 font-medium mb-4 text-xl">{{artist.role}}</p>

      <!-- Description with Read More -->
      <div class="text-gray-600 text-base mb-6">
        <p [class.line-clamp-4]="!expanded">{{artist.description}}</p>
        <button
          *ngIf="artist.description.length > 200"
          (click)="toggleDescription()"
          class="text-primary-600 hover:text-primary-700 text-sm font-medium mt-2 transition-colors duration-200">
          {{expanded ? 'Show Less' : 'Read More'}}
        </button>
      </div>
    </div>

    <!-- Professional Details Grid -->
    <div *ngIf="showFullDetails" class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
      <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="font-semibold text-gray-900 mb-2 flex items-center">
          <svg class="w-4 h-4 text-primary-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
          </svg>
          Specialization
        </h4>
        <p class="text-gray-700 text-sm">{{artist.specialization}}</p>
      </div>

      <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="font-semibold text-gray-900 mb-2 flex items-center">
          <svg class="w-4 h-4 text-primary-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          Experience
        </h4>
        <p class="text-gray-700 text-sm">{{artist.experience}}</p>
      </div>

      <div class="bg-gray-50 rounded-lg p-4 md:col-span-2">
        <h4 class="font-semibold text-gray-900 mb-2 flex items-center">
          <svg class="w-4 h-4 text-primary-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
          </svg>
          Education
        </h4>
        <p class="text-gray-700 text-sm">{{artist.education}}</p>
      </div>
    </div>

    <!-- Quote -->
    <div *ngIf="showFullDetails && artist.quote" class="bg-primary-50 border-l-4 border-primary-500 p-4 mb-6">
      <blockquote class="italic text-gray-700 relative">
        <svg class="w-6 h-6 text-primary-300 absolute -top-2 -left-1" fill="currentColor" viewBox="0 0 24 24">
          <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
        </svg>
        <span class="ml-8">"{{artist.quote}}"</span>
      </blockquote>
    </div>

    <!-- Awards Section -->
    <div *ngIf="showFullDetails && artist.awards && artist.awards.length > 0" class="mb-6">
      <h4 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
        <svg class="w-5 h-5 text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
        </svg>
        Awards & Recognition
      </h4>
      <div class="bg-yellow-50 rounded-lg p-4">
        <ul class="space-y-2">
          <li *ngFor="let award of artist.awards" class="flex items-start text-gray-700 text-sm">
            <span class="text-yellow-600 mr-2 mt-1 flex-shrink-0">🏆</span>
            <span>{{award}}</span>
          </li>
        </ul>
      </div>
    </div>

    <!-- Exhibitions Section -->
    <div *ngIf="showFullDetails && artist.exhibitions && artist.exhibitions.length > 0" class="mb-6">
      <h4 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
        <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
        </svg>
        Exhibitions
      </h4>
      <div class="bg-blue-50 rounded-lg p-4">
        <ul class="space-y-2">
          <li *ngFor="let exhibition of artist.exhibitions" class="flex items-start text-gray-700 text-sm">
            <span class="text-blue-600 mr-2 mt-1 flex-shrink-0">🎨</span>
            <span>{{exhibition}}</span>
          </li>
        </ul>
      </div>
    </div>

    <!-- Social Media Links -->
    <div class="mt-6" *ngIf="artist.socialMedia && artist.socialMedia.length > 0">
      <app-social-media-links
        [socialMedia]="artist.socialMedia"
        size="md"
        layout="horizontal"
        [showLabels]="false">
      </app-social-media-links>
    </div>
  </div>
</div>
