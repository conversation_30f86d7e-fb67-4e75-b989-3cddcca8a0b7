import { Component, OnInit } from '@angular/core';
import { RouterLink, RouterLinkActive } from '@angular/router';
import { CommonModule } from '@angular/common';
import { CartService } from '../../../services/cart.service';

import { MithilaArtBackgroundComponent } from '../../shared/mithila-art-background/mithila-art-background.component';
import { MithilaDecorativeElementComponent } from '../../shared/mithila-decorative-element/mithila-decorative-element.component';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [
    RouterLink,
    RouterLinkActive,
    CommonModule,
    MithilaArtBackgroundComponent,
    MithilaDecorativeElementComponent
  ],
  templateUrl: './header.component.html',
  styleUrl: './header.component.css'
})
export class HeaderComponent implements OnInit {
  isMenuOpen = false;
  cartItemCount = 0;

  constructor(private cartService: CartService) {}

  ngOnInit() {
    this.cartService.cart$.subscribe(cartItems => {
      this.cartItemCount = this.cartService.getCartItemCount();
    });
  }

  toggleMenu() {
    this.isMenuOpen = !this.isMenuOpen;
  }


}
