/* Line clamp utilities for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Hover effects for product cards */
.product-card {
  transition: all 0.3s ease;
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Custom button styles */
.btn-primary {
  @apply bg-primary-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 hover:bg-primary-700 hover:shadow-lg;
}

.btn-secondary {
  @apply bg-secondary-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 hover:bg-secondary-700 hover:shadow-lg;
}

/* Category filter button styles */
.category-btn {
  @apply px-6 py-3 rounded-full border border-primary-200 transition-all duration-300 font-medium;
}

.category-btn.active {
  @apply bg-primary-600 text-white border-primary-600;
}

.category-btn:not(.active) {
  @apply bg-white text-gray-700 hover:bg-primary-50 hover:border-primary-300;
}

/* Product image overlay effects */
.product-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .product-image-overlay {
  opacity: 1;
}

/* Badge styles */
.badge {
  @apply px-3 py-1 rounded-full text-sm font-medium;
}

.badge-featured {
  @apply bg-secondary-500 text-white;
}

.badge-in-stock {
  @apply bg-green-500 text-white;
}

.badge-out-of-stock {
  @apply bg-red-500 text-white;
}

.badge-category {
  @apply bg-white/90 backdrop-blur-sm text-primary-600;
}

/* Responsive grid adjustments */
@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1025px) {
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

/* Loading state styles */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Custom scrollbar for category filters */
.category-filters::-webkit-scrollbar {
  height: 4px;
}

.category-filters::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.category-filters::-webkit-scrollbar-thumb {
  background: #c1440e;
  border-radius: 2px;
}

.category-filters::-webkit-scrollbar-thumb:hover {
  background: #a03a0c;
}

/* Enhanced Product Card Styles from Art Card */

/* Card hover effects */
.product-card-enhanced {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.product-card-enhanced:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(193, 68, 14, 0.1),
    0 0 30px rgba(193, 68, 14, 0.15);
}

/* Image zoom effect */
.image-container {
  overflow: hidden;
  position: relative;
}

.image-container img {
  transition: transform 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-card-enhanced:hover .image-container img {
  transform: scale(1.15);
}

/* Badge animations */
.badge-enhanced {
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.featured-badge {
  animation: featuredPulse 2s infinite;
}

@keyframes featuredPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(244, 180, 0, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(244, 180, 0, 0);
  }
}

/* Quick action buttons */
.quick-actions {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.quick-action-btn {
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.quick-action-btn:hover {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Price highlight effect */
.price-enhanced {
  position: relative;
  transition: all 0.3s ease;
}

.product-card-enhanced:hover .price-enhanced {
  transform: scale(1.05);
  text-shadow: 0 2px 4px rgba(193, 68, 14, 0.3);
}

/* Button hover effects */
.add-to-cart-btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.add-to-cart-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.add-to-cart-btn:hover::before {
  left: 100%;
}

.add-to-cart-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(193, 68, 14, 0.3);
}

/* Glow effect */
.glow-effect {
  transition: all 0.3s ease;
}

.product-card-enhanced:hover .glow-effect {
  box-shadow:
    inset 0 0 20px rgba(193, 68, 14, 0.1),
    0 0 40px rgba(193, 68, 14, 0.2);
}

/* Responsive adjustments for enhanced cards */
@media (max-width: 768px) {
  .product-card-enhanced:hover {
    transform: translateY(-6px) scale(1.01);
  }

  .quick-actions {
    opacity: 1;
    transform: translateY(0);
  }

  .add-to-cart-btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}

/* Accessibility improvements */
.product-card-enhanced:focus-within {
  outline: 2px solid #C1440E;
  outline-offset: 2px;
}

.quick-action-btn:focus,
.add-to-cart-btn:focus {
  outline: 2px solid #C1440E;
  outline-offset: 2px;
}
