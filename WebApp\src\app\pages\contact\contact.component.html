<!-- Enhanced <PERSON> Banner -->
<div class="relative h-[60vh] overflow-hidden">
  <!-- Background Image -->
  <div class="absolute inset-0 bg-cover bg-center bg-no-repeat"
       style="background-image: url('https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg')">
  </div>

  <!-- Gradient Overlay -->
  <div class="absolute inset-0 bg-gradient-to-br from-primary-800/90 via-primary-700/90 to-brick-700/90"></div>

  <!-- <PERSON><PERSON><PERSON> Background -->
  <div class="absolute inset-0 opacity-10">
    <app-mithila-art-background
      [primaryColor]="'#C1440E'"
      [secondaryColor]="'#F4B400'"
      opacity="15">
    </app-mithila-art-background>
  </div>

  <!-- Content -->
  <div class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10">
    <!-- Decorative Element -->
    <app-mithila-decorative-element
      [primaryColor]="'#C1440E'"
      [secondaryColor]="'#F4B400'"
      [type]="'lotus'"
      position="relative mb-6"
      classes="opacity-90"
      size="80px">
    </app-mithila-decorative-element>

    <!-- Title -->
    <h1 class="text-4xl md:text-6xl font-bold text-white mb-6 drop-shadow-lg">
      Get In Touch
    </h1>

    <!-- Subtitle -->
    <p class="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto drop-shadow-md">
      We'd love to hear from you. Send us a message and we'll respond as soon as possible.
    </p>

    <!-- Quick Contact Options -->
    <div class="flex flex-wrap justify-center gap-4">
      <a [href]="'tel:' + (contactInfo?.phone?.[0] || '+9779814830580')"
         class="flex items-center px-6 py-3 bg-white/20 backdrop-blur-sm text-white rounded-lg hover:bg-white/30 transition-all duration-300 border border-white/30">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
        </svg>
        Call Now
      </a>
      <a [href]="'mailto:' + (contactInfo?.email || '<EMAIL>')"
         class="flex items-center px-6 py-3 bg-white/20 backdrop-blur-sm text-white rounded-lg hover:bg-white/30 transition-all duration-300 border border-white/30">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
        </svg>
        Email Us
      </a>
    </div>
  </div>

  <!-- Decorative Border -->
  <app-mithila-border
    [primaryColor]="'#C1440E'"
    [secondaryColor]="'#F4B400'"
    [position]="'bottom'"
    [height]="'40px'"
  ></app-mithila-border>
</div>

<!-- Contact Information Cards -->
<app-mithila-section
  primaryColor="#F4B400"
  secondaryColor="#264653"
  backgroundGradient="from-secondary-50 via-background-light to-accent-50"
  backgroundOpacity="15"
  padding="py-20"
  [showDecorativeElements]="true">

  <app-section-title
    title="Contact Information"
    subtitle="Multiple ways to reach us and visit our gallery"
  ></app-section-title>

  <!-- Contact Cards Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-12">

    <!-- Location Card -->
    <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 p-6 text-center group">
      <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-200 transition-colors duration-300">
        <svg class="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
        </svg>
      </div>
      <h3 class="text-lg font-bold text-gray-900 mb-2">Visit Us</h3>
      <p class="text-gray-600 text-sm mb-4">{{contactInfo?.address || 'Janakpur, Nepal'}}</p>
      <a href="https://maps.google.com" target="_blank"
         class="inline-flex items-center text-primary-600 hover:text-primary-700 font-medium text-sm">
        Get Directions
        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
        </svg>
      </a>
    </div>

    <!-- Phone Card -->
    <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 p-6 text-center group">
      <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-green-200 transition-colors duration-300">
        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
        </svg>
      </div>
      <h3 class="text-lg font-bold text-gray-900 mb-2">Call Us</h3>
      <p class="text-gray-600 text-sm mb-4">
        <span *ngFor="let phone of contactInfo?.phone || []; let last = last">
          {{phone}}<br *ngIf="!last">
        </span>
      </p>
      <a [href]="'tel:' + (contactInfo?.phone?.[0] || '+9779814830580')"
         class="inline-flex items-center text-green-600 hover:text-green-700 font-medium text-sm">
        Call Now
        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
        </svg>
      </a>
    </div>

    <!-- Email Card -->
    <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 p-6 text-center group">
      <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-colors duration-300">
        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
        </svg>
      </div>
      <h3 class="text-lg font-bold text-gray-900 mb-2">Email Us</h3>
      <p class="text-gray-600 text-sm mb-4">{{contactInfo?.email || '<EMAIL>'}}</p>
      <a [href]="'mailto:' + (contactInfo?.email || '<EMAIL>')"
         class="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium text-sm">
        Send Email
        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
        </svg>
      </a>
    </div>

    <!-- Hours Card -->
    <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 p-6 text-center group">
      <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-200 transition-colors duration-300">
        <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </div>
      <h3 class="text-lg font-bold text-gray-900 mb-2">Opening Hours</h3>
      <p class="text-gray-600 text-sm mb-4">Daily: 9:00 AM - 8:00 PM<br>Open all days including holidays</p>
      <span class="inline-flex items-center text-purple-600 font-medium text-sm">
        <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
        Currently Open
      </span>
    </div>
  </div>
</app-mithila-section>

<!-- Map Section -->
<section class="relative overflow-hidden">
  <div class="h-96 w-full">
    <iframe
      src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3571.5668128441087!2d85.92093!3d26.7271!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39ec4144bd00f9b3%3A0x4e9fe97a37b9c128!2sJanaki%20Mandir!5e0!3m2!1sen!2sus!4v1651234567890!5m2!1sen!2sus"
      width="100%"
      height="100%"
      style="border:0;"
      allowfullscreen=""
      loading="lazy"
      referrerpolicy="no-referrer-when-downgrade">
    </iframe>
  </div>

  <!-- Map Overlay Info -->
  <div class="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-lg max-w-xs">
    <h4 class="font-bold text-gray-900 mb-2">Visit Our Gallery</h4>
    <p class="text-sm text-gray-600 mb-2">{{contactInfo?.address || 'Janakpur, Nepal'}}</p>
    <a href="https://maps.google.com" target="_blank"
       class="text-primary-600 hover:text-primary-700 text-sm font-medium flex items-center">
      Get Directions
      <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
      </svg>
    </a>
  </div>

  <!-- Decorative Border -->
  <app-mithila-border
    [primaryColor]="'#F4B400'"
    [secondaryColor]="'#C1440E'"
    [position]="'top'"
    [height]="'30px'"
  ></app-mithila-border>
</section>

<!-- Contact Form Section -->
<app-mithila-section
  primaryColor="#008C8C"
  secondaryColor="#D81B60"
  backgroundGradient="from-peacock-50 via-background-light to-magenta-50"
  backgroundOpacity="15"
  padding="py-20"
  [showDecorativeElements]="true">

  <div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-2xl shadow-2xl overflow-hidden">
      <div class="bg-gradient-to-r from-primary-600 to-secondary-600 p-8 text-white text-center">
        <h2 class="text-3xl font-bold mb-2">Send Us a Message</h2>
        <p class="text-primary-100">We'd love to hear from you. Fill out the form below and we'll get back to you soon.</p>
      </div>

      <div class="p-8">
        <!-- Success Message -->
        <div *ngIf="formSuccess" class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <p class="text-green-800 font-medium">Thank you! Your message has been sent successfully.</p>
          </div>
        </div>

        <form (ngSubmit)="submitForm()" class="space-y-6" @slideUp>
          <!-- Name and Email Row -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
              <input
                type="text"
                id="name"
                name="name"
                [(ngModel)]="contactForm.name"
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                [class.border-red-500]="formSubmitted && formErrors.name"
                placeholder="Enter your full name">
              <p *ngIf="formSubmitted && formErrors.name" class="text-red-500 text-sm mt-1">{{formErrors.name}}</p>
            </div>

            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
              <input
                type="email"
                id="email"
                name="email"
                [(ngModel)]="contactForm.email"
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                [class.border-red-500]="formSubmitted && formErrors.email"
                placeholder="Enter your email address">
              <p *ngIf="formSubmitted && formErrors.email" class="text-red-500 text-sm mt-1">{{formErrors.email}}</p>
            </div>
          </div>

          <!-- Phone and Subject Row -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
              <input
                type="tel"
                id="phone"
                name="phone"
                [(ngModel)]="contactForm.phone"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                placeholder="Your phone number (optional)">
            </div>

            <div>
              <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">Subject *</label>
              <input
                type="text"
                id="subject"
                name="subject"
                [(ngModel)]="contactForm.subject"
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                [class.border-red-500]="formSubmitted && formErrors.subject"
                placeholder="What is this regarding?">
              <p *ngIf="formSubmitted && formErrors.subject" class="text-red-500 text-sm mt-1">{{formErrors.subject}}</p>
            </div>
          </div>

          <!-- Message -->
          <div>
            <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message *</label>
            <textarea
              id="message"
              name="message"
              [(ngModel)]="contactForm.message"
              required
              rows="6"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200 resize-none"
              [class.border-red-500]="formSubmitted && formErrors.message"
              placeholder="Tell us how we can help you..."></textarea>
            <p *ngIf="formSubmitted && formErrors.message" class="text-red-500 text-sm mt-1">{{formErrors.message}}</p>
          </div>

          <!-- Submit Button -->
          <div class="text-center">
            <button
              type="submit"
              [disabled]="formLoading"
              class="px-8 py-4 bg-gradient-to-r from-primary-600 to-secondary-600 text-white font-bold rounded-lg hover:from-primary-700 hover:to-secondary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg flex items-center mx-auto">
              <svg *ngIf="formLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{formLoading ? 'Sending...' : 'Send Message'}}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</app-mithila-section>



<!-- Social Media & Connect Section -->
<app-mithila-section
  primaryColor="#264653"
  secondaryColor="#E76F51"
  backgroundGradient="from-accent-50 via-background-light to-brick-50"
  backgroundOpacity="15"
  padding="py-20"
  [showDecorativeElements]="true">

  <div class="max-w-4xl mx-auto text-center">
    <app-section-title
      title="Connect With Us"
      subtitle="Follow us on social media for updates, events, and behind-the-scenes content"
    ></app-section-title>

    <!-- Social Media Links - Simple Row Layout -->
    <div class="flex justify-center items-center space-x-6 mt-12">
      <a *ngFor="let social of socialLinks"
         [href]="social.url"
         target="_blank"
         class="group transition-all duration-300 transform hover:scale-110">

        <div class="w-14 h-14 rounded-full flex items-center justify-center transition-all duration-300 shadow-lg hover:shadow-xl"
             [ngClass]="social.color">
          <i [class]="social.icon + ' text-xl text-white'"></i>
        </div>
      </a>
    </div>

    <!-- Social Media Text -->
    <p class="text-gray-600 mt-6 text-lg">
      Stay connected with our artistic journey and latest creations
    </p>
  </div>
</app-mithila-section>



<!-- FAQ Section -->
<app-mithila-section
  primaryColor="#3B945E"
  secondaryColor="#F7A700"
  backgroundGradient="from-success-50 via-background-light to-secondary-50"
  backgroundOpacity="15"
  padding="py-20"
  [showDecorativeElements]="true">

  <app-section-title
    title="Frequently Asked Questions"
    subtitle="Find answers to common questions about our gallery and services"
  ></app-section-title>

  <div class="max-w-6xl mx-auto mt-12">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" @staggerIn>
      <div *ngFor="let faq of faqItems; let i = index"
           class="bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 p-6 group">

        <div class="flex items-start">
          <div class="flex-shrink-0 w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center mr-3 group-hover:bg-primary-200 transition-colors duration-300">
            <span class="text-primary-600 font-bold text-xs">{{i + 1}}</span>
          </div>
          <div class="flex-1">
            <h3 class="text-base font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors duration-300">
              {{faq.question}}
            </h3>
            <p class="text-gray-600 text-sm leading-relaxed">{{faq.answer}}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Additional Help -->
    <div class="text-center mt-10">
      <p class="text-gray-600 mb-4">Still have questions?</p>
      <a [href]="'mailto:' + (contactInfo?.email || '<EMAIL>')"
         class="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200 font-medium">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
        </svg>
        Contact Us Directly
      </a>
    </div>
  </div>
</app-mithila-section>

<!-- Visit Gallery CTA -->
<app-mithila-section
  primaryColor="#C1440E"
  secondaryColor="#F4B400"
  backgroundGradient="from-primary-600 via-primary-700 to-secondary-600"
  backgroundOpacity="20"
  padding="py-20"
  classes="bg-gradient-to-r from-primary-600 to-secondary-600"
  [showDecorativeElements]="true">

  <div class="max-w-4xl mx-auto text-center">
    <!-- Decorative Element -->
    <app-mithila-decorative-element
      [primaryColor]="'#F4B400'"
      [secondaryColor]="'#C1440E'"
      [type]="'lotus'"
      position="relative mx-auto mb-6"
      classes="opacity-90"
      size="60px">
    </app-mithila-decorative-element>

    <h2 class="text-3xl font-bold text-white mb-4 drop-shadow-lg">
      Ready to Experience Mithila Art?
    </h2>

    <p class="text-lg text-white/90 mb-8 max-w-2xl mx-auto drop-shadow-md">
      Visit our gallery in {{contactInfo?.address || 'Janakpur'}} to see authentic Mithila artwork, meet our artists, and immerse yourself in this beautiful cultural tradition.
    </p>

    <div class="flex flex-col sm:flex-row justify-center gap-4">
      <a [href]="'tel:' + (contactInfo?.phone?.[0] || '+9779814830580')"
         class="inline-flex items-center px-8 py-4 bg-white text-primary-600 rounded-lg hover:bg-gray-100 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg font-bold">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
        </svg>
        Call to Visit
      </a>

      <a [href]="'mailto:' + (contactInfo?.email || '<EMAIL>')"
         class="inline-flex items-center px-8 py-4 bg-white/20 backdrop-blur-sm text-white border-2 border-white rounded-lg hover:bg-white/30 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg font-bold">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
        </svg>
        Email for Info
      </a>
    </div>
  </div>
</app-mithila-section>
