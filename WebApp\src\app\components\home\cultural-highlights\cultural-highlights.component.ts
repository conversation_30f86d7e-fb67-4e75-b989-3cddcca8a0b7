import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'app-cultural-highlights',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './cultural-highlights.component.html',
  styleUrl: './cultural-highlights.component.css',
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('600ms ease-in', style({ opacity: 1 })),
      ]),
    ]),
    trigger('slideIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateX(50px)' }),
        animate('500ms ease-out', style({ opacity: 1, transform: 'translateX(0)' })),
      ]),
    ]),
  ]
})
export class CulturalHighlightsComponent implements OnInit {
  currentSlide = 0;
  highlights = [
    {
      id: 'cuisine',
      title: '<PERSON><PERSON><PERSON> Cuisine',
      description: 'Experience authentic Maithili flavors with our traditional dishes prepared using local ingredients and age-old recipes passed down through generations.',
      image: 'https://images.unsplash.com/photo-1589302168068-964664d93dc0?q=80&w=1974&auto=format&fit=crop',
      link: '/culture/cuisine'
    },
    {
      id: 'art',
      title: 'Madhubani Art',
      description: 'Discover the vibrant world of Madhubani (Mithila) painting, a traditional art form characterized by geometric patterns, mythological motifs, and natural elements.',
      image: 'https://images.unsplash.com/photo-1625046438416-cf1a3b139c89?q=80&w=2070&auto=format&fit=crop',
      link: '/culture/art'
    },
    {
      id: 'attractions',
      title: 'Janakpur Attractions',
      description: 'Explore the cultural and spiritual landmarks of Janakpur, including the magnificent Janaki Temple, Ram Mandir, and other historical sites.',
      image: 'https://images.unsplash.com/photo-1609609830354-8f615d61f7bc?q=80&w=1974&auto=format&fit=crop',
      link: '/culture/attractions'
    }
  ];
  
  ngOnInit() {
    // Start auto-scroll
    this.startAutoScroll();
  }
  
  startAutoScroll() {
    setInterval(() => {
      this.currentSlide = (this.currentSlide + 1) % this.highlights.length;
    }, 5000); // Change slide every 5 seconds
  }
  
  setCurrentSlide(index: number) {
    this.currentSlide = index;
  }
}
