import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CartService, CartItem } from '../../services/cart.service';
import { DataService, Product } from '../../services/data.service';

@Component({
  selector: 'app-product-detail',
  standalone: true,
  imports: [CommonModule, RouterModule, ReactiveFormsModule],
  templateUrl: './product-detail.component.html',
  styleUrls: ['./product-detail.component.css']
})
export class ProductDetailComponent implements OnInit {
  product: Product | null = null;
  selectedImageIndex = 0;
  quantity = 1;
  showCartModal = false;
  cartItemCount = 0;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private cartService: CartService,
    private dataService: DataService
  ) {}

  ngOnInit() {
    this.route.params.subscribe(params => {
      const productSlug = params['slug'];
      this.product = this.dataService.getProductBySlug(productSlug) || null;

      if (!this.product) {
        this.router.navigate(['/products']);
      }
    });

    // Subscribe to cart changes
    this.cartService.cart$.subscribe(cartItems => {
      this.cartItemCount = this.cartService.getCartItemCount();
    });
  }

  selectImage(index: number) {
    this.selectedImageIndex = index;
  }

  increaseQuantity() {
    this.quantity++;
  }

  decreaseQuantity() {
    if (this.quantity > 1) {
      this.quantity--;
    }
  }

  addToCart() {
    if (this.product) {
      const cartItem: Omit<CartItem, 'quantity'> = {
        id: this.product.id,
        name: this.product.name,
        slug: this.product.slug,
        price: this.product.price,
        image: this.product.images[0],
        artist: Array.isArray(this.product.artist) ? this.product.artist.join(', ') : this.product.artist
      };

      this.cartService.addToCart(cartItem, this.quantity);
      this.showCartModal = true;

      // Hide modal after 3 seconds
      setTimeout(() => {
        this.showCartModal = false;
      }, 3000);
    }
  }

  viewCart() {
    this.router.navigate(['/cart']);
  }

  formatPrice(price: number): string {
    return `NPR ${price.toLocaleString()}`;
  }

  getTotalPrice(): number {
    if (!this.product) return 0;
    return this.product.price * this.quantity;
  }

  isInCart(): boolean {
    return this.product ? this.cartService.isInCart(this.product.id) : false;
  }

  getCartQuantity(): number {
    return this.product ? this.cartService.getItemQuantity(this.product.id) : 0;
  }

  // Helper methods for artist display
  isArray = Array.isArray;

  getArtistArray(artist: string | string[]): string[] {
    return Array.isArray(artist) ? artist : [artist];
  }
}
