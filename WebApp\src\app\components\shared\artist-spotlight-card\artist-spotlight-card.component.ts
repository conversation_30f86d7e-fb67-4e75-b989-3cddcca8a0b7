import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ArtistSpotlight } from '../../../services/data.service';
import { SocialMediaLinksComponent } from '../social-media-links/social-media-links.component';

@Component({
  selector: 'app-artist-spotlight-card',
  standalone: true,
  imports: [CommonModule, SocialMediaLinksComponent],
  templateUrl: './artist-spotlight-card.component.html',
  styleUrl: './artist-spotlight-card.component.css'
})
export class ArtistSpotlightCardComponent {
  @Input() artist!: ArtistSpotlight;
  @Input() showFullDetails: boolean = true;
  @Input() cardStyle: 'default' | 'compact' = 'default';
  
  expanded = false;

  toggleDescription() {
    this.expanded = !this.expanded;
  }
}
