import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-mithila-decorative-element',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="{{position}} {{classes}}" [ngStyle]="{'width': size, 'height': size}">
      <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg" class="w-full h-full">
        <defs>
          <linearGradient id="element-gradient-{{uniqueId}}" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" [attr.stop-color]="primaryColor" stop-opacity="0.7">
              <animate attributeName="stop-opacity" values="0.7;0.3;0.7" dur="5s" repeatCount="indefinite" />
            </stop>
            <stop offset="100%" [attr.stop-color]="secondaryColor" stop-opacity="0.7">
              <animate attributeName="stop-opacity" values="0.7;0.3;0.7" dur="5s" repeatCount="indefinite" />
            </stop>
          </linearGradient>
        </defs>
        
        <!-- Lotus Element -->
        <g *ngIf="type === 'lotus'">
          <!-- Lotus Petals -->
          <g>
            <path d="M50,10 C65,15 75,30 75,50 C75,70 65,85 50,90 C35,85 25,70 25,50 C25,30 35,15 50,10Z" 
                  fill="none" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="1">
              <animate attributeName="stroke-width" values="1;1.5;1" dur="4s" repeatCount="indefinite" />
            </path>
            <path d="M50,20 C60,25 70,35 70,50 C70,65 60,75 50,80 C40,75 30,65 30,50 C30,35 40,25 50,20Z" 
                  fill="none" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.8">
              <animate attributeName="stroke-width" values="0.8;1.2;0.8" dur="4s" repeatCount="indefinite" />
            </path>
            <path d="M50,30 C57,33 63,40 63,50 C63,60 57,67 50,70 C43,67 37,60 37,50 C37,40 43,33 50,30Z" 
                  fill="none" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.6">
              <animate attributeName="stroke-width" values="0.6;1;0.6" dur="4s" repeatCount="indefinite" />
            </path>
          </g>
          
          <!-- Center -->
          <circle cx="50" cy="50" r="5" [attr.fill]="'url(#element-gradient-' + uniqueId + ')'" opacity="0.6">
            <animate attributeName="r" values="5;6;5" dur="3s" repeatCount="indefinite" />
            <animate attributeName="opacity" values="0.6;0.8;0.6" dur="3s" repeatCount="indefinite" />
          </circle>
        </g>
        
        <!-- Peacock Element -->
        <g *ngIf="type === 'peacock'">
          <!-- Body -->
          <path d="M50,70 C40,70 30,60 30,50 C30,40 40,30 50,30 C60,30 70,40 70,50 C70,60 60,70 50,70Z" 
                fill="none" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="1">
            <animate attributeName="stroke-width" values="1;1.5;1" dur="4s" repeatCount="indefinite" />
          </path>
          
          <!-- Feathers -->
          <g>
            <path d="M50,30 C55,15 65,10 75,15 C65,20 60,25 50,30Z" 
                  fill="none" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.8">
              <animate attributeName="stroke-width" values="0.8;1.2;0.8" dur="4s" repeatCount="indefinite" />
            </path>
            <path d="M50,30 C60,20 75,15 85,25 C70,25 60,25 50,30Z" 
                  fill="none" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.8">
              <animate attributeName="stroke-width" values="0.8;1.2;0.8" dur="4s" repeatCount="indefinite" delay="0.5s" />
            </path>
            <path d="M50,30 C60,25 70,20 70,10 C65,20 55,25 50,30Z" 
                  fill="none" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.8">
              <animate attributeName="stroke-width" values="0.8;1.2;0.8" dur="4s" repeatCount="indefinite" delay="1s" />
            </path>
            <path d="M50,30 C45,15 35,10 25,15 C35,20 40,25 50,30Z" 
                  fill="none" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.8">
              <animate attributeName="stroke-width" values="0.8;1.2;0.8" dur="4s" repeatCount="indefinite" delay="1.5s" />
            </path>
            <path d="M50,30 C40,20 25,15 15,25 C30,25 40,25 50,30Z" 
                  fill="none" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.8">
              <animate attributeName="stroke-width" values="0.8;1.2;0.8" dur="4s" repeatCount="indefinite" delay="2s" />
            </path>
            <path d="M50,30 C40,25 30,20 30,10 C35,20 45,25 50,30Z" 
                  fill="none" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.8">
              <animate attributeName="stroke-width" values="0.8;1.2;0.8" dur="4s" repeatCount="indefinite" delay="2.5s" />
            </path>
          </g>
          
          <!-- Eye -->
          <circle cx="50" cy="50" r="3" [attr.fill]="'url(#element-gradient-' + uniqueId + ')'" opacity="0.8">
            <animate attributeName="r" values="3;4;3" dur="3s" repeatCount="indefinite" />
          </circle>
        </g>
        
        <!-- Fish Element -->
        <g *ngIf="type === 'fish'">
          <!-- Body -->
          <path d="M30,50 C40,35 60,35 70,50 C60,65 40,65 30,50Z" 
                fill="none" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="1">
            <animate attributeName="stroke-width" values="1;1.5;1" dur="4s" repeatCount="indefinite" />
          </path>
          
          <!-- Tail -->
          <path d="M70,50 C80,40 85,40 90,50 C85,60 80,60 70,50Z" 
                fill="none" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.8">
            <animate attributeName="stroke-width" values="0.8;1.2;0.8" dur="4s" repeatCount="indefinite" />
          </path>
          
          <!-- Eye -->
          <circle cx="35" cy="50" r="2" [attr.fill]="'url(#element-gradient-' + uniqueId + ')'" opacity="0.8">
            <animate attributeName="r" values="2;3;2" dur="3s" repeatCount="indefinite" />
          </circle>
          
          <!-- Fins -->
          <path d="M50,35 C55,30 60,30 65,35 C60,40 55,40 50,35Z" 
                fill="none" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.6">
            <animate attributeName="stroke-width" values="0.6;1;0.6" dur="4s" repeatCount="indefinite" />
          </path>
          <path d="M50,65 C55,70 60,70 65,65 C60,60 55,60 50,65Z" 
                fill="none" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.6">
            <animate attributeName="stroke-width" values="0.6;1;0.6" dur="4s" repeatCount="indefinite" delay="0.5s" />
          </path>
        </g>
        
        <!-- Elephant Element -->
        <g *ngIf="type === 'elephant'">
          <!-- Body -->
          <path d="M30,60 C30,50 40,40 55,40 C70,40 80,50 80,60 C80,70 70,75 55,75 C40,75 30,70 30,60Z" 
                fill="none" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="1">
            <animate attributeName="stroke-width" values="1;1.5;1" dur="4s" repeatCount="indefinite" />
          </path>
          
          <!-- Head -->
          <path d="M30,60 C25,55 25,50 30,45 C35,40 40,40 45,45 C45,50 40,55 30,60Z" 
                fill="none" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.8">
            <animate attributeName="stroke-width" values="0.8;1.2;0.8" dur="4s" repeatCount="indefinite" />
          </path>
          
          <!-- Trunk -->
          <path d="M30,45 C25,40 20,40 15,45 C10,50 10,60 15,65 C20,70 25,70 30,65" 
                fill="none" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.8">
            <animate attributeName="stroke-width" values="0.8;1.2;0.8" dur="4s" repeatCount="indefinite" />
          </path>
          
          <!-- Ear -->
          <path d="M45,45 C50,35 60,35 65,45 C65,55 60,60 45,45Z" 
                fill="none" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.6">
            <animate attributeName="stroke-width" values="0.6;1;0.6" dur="4s" repeatCount="indefinite" />
          </path>
          
          <!-- Eye -->
          <circle cx="35" cy="45" r="1" [attr.fill]="'url(#element-gradient-' + uniqueId + ')'" opacity="0.8">
            <animate attributeName="r" values="1;1.5;1" dur="3s" repeatCount="indefinite" />
          </circle>
          
          <!-- Decorations -->
          <path d="M55,40 L55,30 M60,40 L60,30 M65,40 L65,30 M70,40 L70,30" 
                [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.5" stroke-dasharray="2,2">
            <animate attributeName="stroke-width" values="0.5;0.8;0.5" dur="4s" repeatCount="indefinite" />
          </path>
        </g>
        
        <!-- Geometric Element -->
        <g *ngIf="type === 'geometric'">
          <!-- Concentric Circles -->
          <circle cx="50" cy="50" r="40" fill="none" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.8">
            <animate attributeName="stroke-width" values="0.8;1.2;0.8" dur="4s" repeatCount="indefinite" />
          </circle>
          <circle cx="50" cy="50" r="30" fill="none" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.6">
            <animate attributeName="stroke-width" values="0.6;1;0.6" dur="4s" repeatCount="indefinite" delay="0.5s" />
          </circle>
          <circle cx="50" cy="50" r="20" fill="none" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.4">
            <animate attributeName="stroke-width" values="0.4;0.8;0.4" dur="4s" repeatCount="indefinite" delay="1s" />
          </circle>
          
          <!-- Radial Lines -->
          <g>
            <path d="M50,50 L50,10" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.5">
              <animate attributeName="stroke-width" values="0.5;0.8;0.5" dur="4s" repeatCount="indefinite" />
            </path>
            <path d="M50,50 L90,50" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.5">
              <animate attributeName="stroke-width" values="0.5;0.8;0.5" dur="4s" repeatCount="indefinite" delay="0.5s" />
            </path>
            <path d="M50,50 L50,90" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.5">
              <animate attributeName="stroke-width" values="0.5;0.8;0.5" dur="4s" repeatCount="indefinite" delay="1s" />
            </path>
            <path d="M50,50 L10,50" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.5">
              <animate attributeName="stroke-width" values="0.5;0.8;0.5" dur="4s" repeatCount="indefinite" delay="1.5s" />
            </path>
            <path d="M50,50 L75,25" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.5">
              <animate attributeName="stroke-width" values="0.5;0.8;0.5" dur="4s" repeatCount="indefinite" delay="2s" />
            </path>
            <path d="M50,50 L75,75" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.5">
              <animate attributeName="stroke-width" values="0.5;0.8;0.5" dur="4s" repeatCount="indefinite" delay="2.5s" />
            </path>
            <path d="M50,50 L25,75" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.5">
              <animate attributeName="stroke-width" values="0.5;0.8;0.5" dur="4s" repeatCount="indefinite" delay="3s" />
            </path>
            <path d="M50,50 L25,25" [attr.stroke]="'url(#element-gradient-' + uniqueId + ')'" stroke-width="0.5">
              <animate attributeName="stroke-width" values="0.5;0.8;0.5" dur="4s" repeatCount="indefinite" delay="3.5s" />
            </path>
          </g>
          
          <!-- Center Dot -->
          <circle cx="50" cy="50" r="3" [attr.fill]="'url(#element-gradient-' + uniqueId + ')'" opacity="0.8">
            <animate attributeName="r" values="3;4;3" dur="3s" repeatCount="indefinite" />
          </circle>
        </g>
      </svg>
    </div>
  `,
  styles: []
})
export class MithilaDecorativeElementComponent {
  @Input() primaryColor: string = '#C1440E'; // Terracotta Red
  @Input() secondaryColor: string = '#F4B400'; // Sunflower Yellow
  @Input() type: 'lotus' | 'peacock' | 'fish' | 'elephant' | 'geometric' = 'lotus';
  @Input() position: string = 'absolute'; // CSS position classes
  @Input() classes: string = ''; // Additional CSS classes
  @Input() size: string = '100px'; // Size of the element
  
  // Generate a unique ID for SVG elements
  uniqueId: string = Math.random().toString(36).substring(2, 9);
}
