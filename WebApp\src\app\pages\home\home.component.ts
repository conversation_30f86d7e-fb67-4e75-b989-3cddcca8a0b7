import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { SectionTitleComponent } from '../../components/shared/section-title/section-title.component';
import { DataService, CompanyInfo, Testimonial, Event, ArtistSpotlight } from '../../services/data.service';
import { ArtistSpotlightCardComponent } from '../../components/shared/artist-spotlight-card/artist-spotlight-card.component';
import { CommonModule } from '@angular/common';
import { trigger, transition, style, animate, query, stagger, state, keyframes } from '@angular/animations';
import { MithilaSectionComponent } from '../../components/shared/mithila-section/mithila-section.component';
import { MithilaDecorativeElementComponent } from '../../components/shared/mithila-decorative-element/mithila-decorative-element.component';
import { CartService } from '../../services/cart.service';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    MithilaSectionComponent,
    MithilaDecorativeElementComponent,
    SectionTitleComponent,
    ArtistSpotlightCardComponent,
  ],
  templateUrl: './home.component.html',
  styleUrl: './home.component.css',
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('600ms ease-in', style({ opacity: 1 })),
      ]),
    ]),
    trigger('staggerIn', [
      transition('* => *', [
        query(':enter', [
          style({ opacity: 0, transform: 'translateY(20px)' }),
          stagger('200ms', [
            animate('400ms ease-out', style({ opacity: 1, transform: 'translateY(0)' })),
          ]),
        ], { optional: true }),
      ]),
    ]),
    trigger('scaleIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'scale(0.9)' }),
        animate('500ms ease-out', style({ opacity: 1, transform: 'scale(1)' })),
      ]),
    ]),
    trigger('textTyping', [
      state('void', style({
        width: '0',
      })),
      transition('void => *', [
        animate('1.5s steps(20)', style({
          width: '100%',
        }))
      ]),
    ]),
    trigger('wordRotate', [
      transition('* => *', [
        animate('500ms', keyframes([
          style({ opacity: 0, transform: 'translateY(20px)', offset: 0 }),
          style({ opacity: 1, transform: 'translateY(0)', offset: 0.5 }),
          style({ opacity: 1, transform: 'translateY(0)', offset: 0.9 }),
          style({ opacity: 0, transform: 'translateY(-20px)', offset: 1 })
        ]))
      ])
    ]),
    trigger('fadeSlideIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(30px)' }),
        animate('800ms 300ms ease-out', style({ opacity: 1, transform: 'translateY(0)' })),
      ]),
    ]),
    trigger('fadeSlideInRight', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateX(30px)' }),
        animate('800ms 300ms ease-out', style({ opacity: 1, transform: 'translateX(0)' })),
      ]),
    ]),
    trigger('fadeSlideInLeft', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateX(-30px)' }),
        animate('800ms 300ms ease-out', style({ opacity: 1, transform: 'translateX(0)' })),
      ]),
    ]),
  ]
})
export class HomeComponent implements OnInit, OnDestroy {
  currentWordIndex = 0;
  rotatingWords = ['Art', 'Painting', 'Culture', 'Mithila', 'Tradition', 'Heritage'];
  currentWord = '';
  typingInterval: any;
  wordChangeInterval: any;

  // Data from service
  companyInfo: CompanyInfo | null = null;
  testimonials: Testimonial[] = [];
  upcomingEvents: Event[] = [];
  spotlightArtist: ArtistSpotlight | null = null;

  constructor(
    private dataService: DataService,
    private router: Router,
    private cartService: CartService
  ) {}

  ngOnInit() {
    this.startWordRotation();
    this.loadData();
  }

  loadData() {
    this.companyInfo = this.dataService.getCompanyInfo();
    this.testimonials = this.dataService.getTestimonials();
    this.upcomingEvents = this.dataService.getUpcomingEvents();
    this.spotlightArtist = this.dataService.getArtistSpotlight();
  }

  // Getter methods for template access
  get featuredProducts() {
    return this.dataService.getFeaturedProducts().slice(0, 3);
  }

  startWordRotation() {
    this.wordChangeInterval = setInterval(() => {
      this.currentWordIndex = (this.currentWordIndex + 1) % this.rotatingWords.length;
      this.currentWord = this.rotatingWords[this.currentWordIndex];
    }, 3000);
  }

  ngOnDestroy() {
    if (this.typingInterval) clearInterval(this.typingInterval);
    if (this.wordChangeInterval) clearInterval(this.wordChangeInterval);
  }

  // Navigation method
  navigateToProduct(slug: string) {
    this.router.navigate(['/products', slug]);
  }

  // Cart methods
  isInCart(productId: string): boolean {
    return this.cartService.isInCart(productId);
  }

  addToCart(product: any, event: MouseEvent) {
    event.stopPropagation();
    this.cartService.addToCart(product);
  }

  // Helper methods for artist display
  isArray = Array.isArray;

  getArtistArray(artist: string | string[]): string[] {
    return Array.isArray(artist) ? artist : [artist];
  }
}
