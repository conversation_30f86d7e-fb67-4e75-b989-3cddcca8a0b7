.floating-words-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  z-index: 4;
}

.floating-word {
  position: absolute;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 300;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  animation: pulse 8s ease-in-out infinite;
  transform-origin: center;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.2;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}
