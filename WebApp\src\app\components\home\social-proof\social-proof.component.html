<section class="py-12 bg-white border-t border-gray-100">
  <div class="container mx-auto px-4">
    <div class="flex flex-col md:flex-row items-center justify-center md:justify-between">
      <!-- Badges -->
      <div class="flex items-center space-x-8 mb-6 md:mb-0" @fadeIn>
        <a *ngFor="let badge of badges" [href]="badge.link" target="_blank" class="opacity-70 hover:opacity-100 transition-opacity duration-300">
          <img [src]="badge.logo" [alt]="badge.name" class="h-10 md:h-12">
        </a>
      </div>
      
      <!-- Counter -->
      <div class="flex items-center" @countUp>
        <div class="text-3xl md:text-4xl font-heading font-bold text-secondary-500 mr-3">{{guestCount}}+</div>
        <div class="text-lg text-gray-600">Happy Guests</div>
      </div>
    </div>
  </div>
</section>
