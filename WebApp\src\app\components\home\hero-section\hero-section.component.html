<section class="relative h-[600px] md:h-[700px] lg:h-[800px] overflow-hidden">
  <!-- Video or Slideshow Background -->
  <div class="absolute inset-0 w-full h-full">
    <!-- Slideshow -->
    <div *ngFor="let slide of slides; let i = index" 
         class="absolute inset-0 w-full h-full transition-opacity duration-1000 ease-in-out bg-cover bg-center"
         [style.opacity]="i === currentSlide ? 1 : 0"
         [style.backgroundImage]="'url(' + slide.image + ')'">
    </div>
    
    <!-- Overlay Gradient -->
    <div class="absolute inset-0 bg-gradient-to-r from-primary-500/50 to-secondary-500/50"></div>
  </div>
  
  <!-- Parallax Elements -->
  <div class="absolute inset-0 w-full h-full" style="transform: translateY(var(--parallax-offset, 0));">
    <!-- Decorative Elements -->
    <div class="absolute top-1/4 left-1/4 w-32 h-32 border-2 border-white/20 rounded-full"></div>
    <div class="absolute bottom-1/4 right-1/4 w-48 h-48 border-2 border-white/20 rounded-full"></div>
  </div>
  
  <!-- Content -->
  <div class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10">
    <h1 class="text-4xl md:text-5xl lg:text-6xl font-heading font-bold text-white mb-4 leading-tight" @fadeIn>
      Welcome to Mithilani Ghar
    </h1>
    <h2 class="text-xl md:text-2xl lg:text-3xl font-heading text-white/90 mb-8" @slideUp>
      Heart of Mithila Culture
    </h2>
    <p class="text-lg md:text-xl text-white/80 max-w-2xl mb-12" @slideUp>
      Experience authentic Maithili hospitality near Janaki Temple
    </p>
    <button class="btn btn-secondary text-background-dark px-8 py-4 text-lg font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 animate-pulse" @pulse>
      Book Now
    </button>
  </div>
</section>
