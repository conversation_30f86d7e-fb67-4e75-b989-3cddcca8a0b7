import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { DataService } from '../../../services/data.service';

@Component({
  selector: 'app-artist-application-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './artist-application-form.component.html',
  styleUrl: './artist-application-form.component.css'
})
export class ArtistApplicationFormComponent implements OnInit {
  @Input() isOpen = false;
  @Input() title = 'Join as Artist';
  @Output() close = new EventEmitter<void>();
  @Output() submit = new EventEmitter<any>();

  artistForm!: FormGroup;
  isSubmitting = false;
  isSubmitted = false;
  artStyles: string[] = [];
  experienceLevels: { value: string; label: string }[] = [];

  constructor(
    private fb: FormBuilder,
    private dataService: DataService
  ) {}

  ngOnInit() {
    this.artStyles = this.dataService.getArtStyles();
    this.experienceLevels = this.dataService.getExperienceLevels();
    this.initForm();
  }

  initForm() {
    this.artistForm = this.fb.group({
      // Personal Information
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],
      dateOfBirth: ['', Validators.required],
      
      // Address
      address: ['', Validators.required],
      city: ['', Validators.required],
      postalCode: ['', Validators.required],
      country: ['Nepal', Validators.required],
      
      // Artist Information
      artistName: ['', Validators.required],
      experienceLevel: ['', Validators.required],
      artStyles: [[], Validators.required],
      yearsOfExperience: ['', [Validators.required, Validators.min(0)]],
      
      // Portfolio
      portfolioDescription: ['', [Validators.required, Validators.minLength(50)]],
      portfolioWebsite: [''],
      socialMediaLinks: [''],

      // Motivation
      whyJoin: ['', [Validators.required, Validators.minLength(100)]],
      goals: ['', Validators.required],

      // Availability
      availability: ['', Validators.required],
      canTeach: [false],
      canParticipateEvents: [false],

    });
  }

  onArtStyleChange(style: string, event: any) {
    const currentStyles = this.artistForm.get('artStyles')?.value || [];
    if (event.target.checked) {
      this.artistForm.patchValue({
        artStyles: [...currentStyles, style]
      });
    } else {
      this.artistForm.patchValue({
        artStyles: currentStyles.filter((s: string) => s !== style)
      });
    }
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.artistForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.artistForm.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) return `${fieldName} is required`;
      if (field.errors['email']) return 'Please enter a valid email';
      if (field.errors['minlength']) return `Minimum ${field.errors['minlength'].requiredLength} characters required`;
      if (field.errors['pattern']) return 'Please enter a valid phone number';
      if (field.errors['min']) return 'Value must be greater than 0';
    }
    return '';
  }

  async onSubmit() {
    if (this.artistForm.valid) {
      this.isSubmitting = true;
      
      // Simulate API call
      setTimeout(() => {
        this.submit.emit(this.artistForm.value);
        this.isSubmitting = false;
        this.isSubmitted = true;
        
        // Auto close after 3 seconds
        setTimeout(() => {
          this.closeForm();
        }, 3000);
      }, 2000);
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.artistForm.controls).forEach(key => {
        this.artistForm.get(key)?.markAsTouched();
      });
    }
  }

  closeForm() {
    this.close.emit();
    this.isSubmitted = false;
    this.artistForm.reset();
    this.initForm();
  }
}
