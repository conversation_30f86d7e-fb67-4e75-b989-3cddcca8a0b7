import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CartService, CartItem } from '../../services/cart.service';

@Component({
  selector: 'app-cart',
  standalone: true,
  imports: [CommonModule, RouterModule, ReactiveFormsModule],
  templateUrl: './cart.component.html',
  styleUrls: ['./cart.component.css']
})
export class CartComponent implements OnInit {
  cartItems: CartItem[] = [];
  checkoutForm: FormGroup;
  showCheckoutForm = false;
  orderSubmitted = false;

  constructor(
    private cartService: CartService,
    private fb: FormBuilder
  ) {
    this.checkoutForm = this.fb.group({
      customerName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],
      address: ['', [Validators.required, Validators.minLength(10)]],
      city: ['', [Validators.required]],
      postalCode: ['', [Validators.required]],
      country: ['', [Validators.required]],
      specialRequests: [''],
      agreeToTerms: [false, [Validators.requiredTrue]]
    });
  }

  ngOnInit() {
    this.cartService.cart$.subscribe(items => {
      this.cartItems = items;
    });
  }

  updateQuantity(itemId: string, quantity: number) {
    this.cartService.updateQuantity(itemId, quantity);
  }

  removeItem(itemId: string) {
    this.cartService.removeFromCart(itemId);
  }

  getCartTotal(): number {
    return this.cartService.getCartTotal();
  }

  getCartItemCount(): number {
    return this.cartService.getCartItemCount();
  }

  formatPrice(price: number): string {
    return `NPR ${price.toLocaleString()}`;
  }

  proceedToCheckout() {
    if (this.cartItems.length > 0) {
      this.showCheckoutForm = true;
      setTimeout(() => {
        const formElement = document.getElementById('checkout-form');
        if (formElement) {
          formElement.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);
    }
  }

  onSubmitOrder() {
    if (this.checkoutForm.valid && this.cartItems.length > 0) {
      // In a real application, you would send this data to your backend
      console.log('Order submitted:', {
        items: this.cartItems,
        customerDetails: this.checkoutForm.value,
        total: this.getCartTotal()
      });
      
      this.orderSubmitted = true;
      this.showCheckoutForm = false;
      this.cartService.clearCart();
      
      // Scroll to success message
      setTimeout(() => {
        const successElement = document.getElementById('order-success');
        if (successElement) {
          successElement.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.checkoutForm.controls).forEach(key => {
        this.checkoutForm.get(key)?.markAsTouched();
      });
    }
  }

  getFieldError(fieldName: string): string {
    const field = this.checkoutForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) return `${fieldName} is required`;
      if (field.errors['email']) return 'Please enter a valid email';
      if (field.errors['pattern']) return 'Please enter a valid phone number';
      if (field.errors['minlength']) return `${fieldName} is too short`;
    }
    return '';
  }

  continueShopping() {
    this.showCheckoutForm = false;
  }
}
