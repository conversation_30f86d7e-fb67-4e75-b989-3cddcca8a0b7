from django.core.management.base import BaseCommand
from django.utils.text import slugify
from mithlani_api.models import (
    SocialMediaLink, Artist, ArtistSpotlight, TeamMember, Category, Product,
    GalleryImage, FAQ, ContactInfo, Testimonial, Event, CompanyFeature,
    CompanyInfo, AboutUsStory, AboutUsMission, Achievement, Facility,
    ProductArtist, ArtistSocialMedia, ArtistSpotlightSocialMedia, TeamMemberSocialMedia
)


class Command(BaseCommand):
    help = 'Populate database with sample data matching Angular data service'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting data population...'))
        
        # Clear existing data
        self.clear_data()
        
        # Create data
        self.create_social_media_links()
        self.create_categories()
        self.create_artists()
        self.create_artist_spotlight()
        self.create_team_members()
        self.create_products()
        self.create_gallery_images()
        self.create_faq()
        self.create_contact_info()
        self.create_testimonials()
        self.create_events()
        self.create_company_info()
        self.create_about_us_data()
        
        self.stdout.write(self.style.SUCCESS('Data population completed successfully!'))

    def clear_data(self):
        """Clear existing data"""
        self.stdout.write('Clearing existing data...')
        
        # Clear in reverse order of dependencies
        ProductArtist.objects.all().delete()
        ArtistSocialMedia.objects.all().delete()
        ArtistSpotlightSocialMedia.objects.all().delete()
        TeamMemberSocialMedia.objects.all().delete()
        
        Product.objects.all().delete()
        Artist.objects.all().delete()
        ArtistSpotlight.objects.all().delete()
        TeamMember.objects.all().delete()
        Category.objects.all().delete()
        GalleryImage.objects.all().delete()
        FAQ.objects.all().delete()
        ContactInfo.objects.all().delete()
        Testimonial.objects.all().delete()
        Event.objects.all().delete()
        CompanyFeature.objects.all().delete()
        CompanyInfo.objects.all().delete()
        AboutUsStory.objects.all().delete()
        AboutUsMission.objects.all().delete()
        Achievement.objects.all().delete()
        Facility.objects.all().delete()
        SocialMediaLink.objects.all().delete()

    def create_social_media_links(self):
        """Create social media links"""
        self.stdout.write('Creating social media links...')
        
        social_links = [
            {
                'platform': 'Instagram',
                'url': 'https://instagram.com/example',
                'icon_class': 'fab fa-instagram',
                'color': 'bg-pink-600',
                'hover_color': 'hover:bg-pink-700',
                'name': 'Instagram'
            },
            {
                'platform': 'Facebook',
                'url': 'https://facebook.com/example',
                'icon_class': 'fab fa-facebook-f',
                'color': 'bg-blue-600',
                'hover_color': 'hover:bg-blue-700',
                'name': 'Facebook'
            },
            {
                'platform': 'Twitter',
                'url': 'https://twitter.com/example',
                'icon_class': 'fab fa-twitter',
                'color': 'bg-sky-500',
                'hover_color': 'hover:bg-sky-600',
                'name': 'Twitter'
            },
            {
                'platform': 'LinkedIn',
                'url': 'https://linkedin.com/in/example',
                'icon_class': 'fab fa-linkedin-in',
                'color': 'bg-blue-700',
                'hover_color': 'hover:bg-blue-800',
                'name': 'LinkedIn'
            },
            {
                'platform': 'Website',
                'url': 'https://example.com',
                'icon_class': 'fas fa-globe',
                'color': 'bg-gray-600',
                'hover_color': 'hover:bg-gray-700',
                'name': 'Website'
            }
        ]
        
        for link_data in social_links:
            SocialMediaLink.objects.create(**link_data)

    def create_categories(self):
        """Create product categories"""
        self.stdout.write('Creating categories...')
        
        categories = ['Paintings', 'Clay Crafts', 'Textiles', 'Wood Crafts']
        
        for cat_name in categories:
            Category.objects.create(
                name=cat_name,
                slug=slugify(cat_name),
                description=f'Beautiful {cat_name.lower()} from Mithila region'
            )

    def create_artists(self):
        """Create artists"""
        self.stdout.write('Creating artists...')
        
        artists_data = [
            {
                'name': 'Kamala Devi',
                'role': 'Traditional Painter',
                'description': 'Kamala specializes in traditional Madhubani paintings, creating vibrant artworks that celebrate the rich cultural heritage of Mithila region.',
                'image': 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg'
            },
            {
                'name': 'Rajesh Kumar',
                'role': 'Contemporary Artist',
                'description': 'Rajesh brings a modern twist to traditional Mithila art, blending contemporary techniques with ancient motifs.',
                'image': 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg'
            },
            {
                'name': 'Sunita Sharma',
                'role': 'Clay Artist',
                'description': 'Sunita creates beautiful clay sculptures and pottery inspired by Mithila traditions and folklore.',
                'image': 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg'
            },
            {
                'name': 'Meera Sharma',
                'role': 'Textile Artist',
                'description': 'Meera specializes in hand-painted textiles and fabric art, bringing Mithila designs to contemporary fashion and home decor.',
                'image': 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg'
            }
        ]
        
        instagram_link = SocialMediaLink.objects.get(platform='Instagram')
        facebook_link = SocialMediaLink.objects.get(platform='Facebook')
        
        for artist_data in artists_data:
            artist = Artist.objects.create(**artist_data)
            
            # Add social media links
            ArtistSocialMedia.objects.create(artist=artist, social_media=instagram_link)
            ArtistSocialMedia.objects.create(artist=artist, social_media=facebook_link)

    def create_artist_spotlight(self):
        """Create artist spotlight"""
        self.stdout.write('Creating artist spotlight...')
        
        spotlight_data = {
            'name': 'Sarita Devi',
            'role': 'Master Artist & Founder',
            'description': 'Sarita Devi is a renowned master artist and the visionary founder of Mithilani Ghar. With over 25 years of experience in traditional Mithila art, she has dedicated her life to preserving and promoting this ancient art form.',
            'image': 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
            'specialization': 'Traditional Madhubani Painting, Kohbar Art, Cultural Preservation',
            'experience': '25+ years',
            'education': 'Master of Fine Arts from Lalit Kala Academy, Traditional training under renowned Mithila artists',
            'awards': [
                'National Award for Traditional Arts (2018)',
                'UNESCO Recognition for Cultural Preservation (2020)',
                'State Award for Women Entrepreneurs (2019)',
                'Excellence in Folk Art Award (2017)'
            ],
            'exhibitions': [
                'South Asian Art Festival, London (2019)',
                'Traditional Arts Exhibition, Kathmandu (2018)',
                'Cultural Heritage Showcase, New Delhi (2017)',
                'International Folk Art Festival, Paris (2020)',
                'Heritage Arts Expo, Bangkok (2021)'
            ],
            'quote': 'Art is not just about creating beauty; it\'s about preserving our cultural identity and passing it on to future generations. Every brushstroke carries the wisdom of our ancestors.'
        }
        
        spotlight = ArtistSpotlight.objects.create(**spotlight_data)
        
        # Add social media links
        instagram_link = SocialMediaLink.objects.get(platform='Instagram')
        facebook_link = SocialMediaLink.objects.get(platform='Facebook')
        website_link = SocialMediaLink.objects.get(platform='Website')
        
        ArtistSpotlightSocialMedia.objects.create(artist_spotlight=spotlight, social_media=instagram_link)
        ArtistSpotlightSocialMedia.objects.create(artist_spotlight=spotlight, social_media=facebook_link)
        ArtistSpotlightSocialMedia.objects.create(artist_spotlight=spotlight, social_media=website_link)

    def create_team_members(self):
        """Create team members"""
        self.stdout.write('Creating team members...')

        team_data = [
            {
                'name': 'Rajesh Kumar',
                'role': 'Director',
                'description': 'Rajesh oversees the overall operations of Mithilani Ghar and manages strategic partnerships with cultural institutions worldwide.',
                'image': 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
                'specialization': 'Cultural Management & Strategic Planning'
            },
            {
                'name': 'Priya Sharma',
                'role': 'Manager',
                'description': 'Priya manages day-to-day operations, coordinates workshops, and ensures smooth functioning of all gallery activities.',
                'image': 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
                'specialization': 'Operations Management & Event Coordination'
            },
            {
                'name': 'Amit Singh',
                'role': 'Distribution Head',
                'description': 'Amit handles the distribution and logistics of artworks, ensuring safe delivery to customers worldwide.',
                'image': 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
                'specialization': 'Logistics & International Shipping'
            }
        ]

        linkedin_link = SocialMediaLink.objects.get(platform='LinkedIn')
        facebook_link = SocialMediaLink.objects.get(platform='Facebook')

        for member_data in team_data:
            member = TeamMember.objects.create(**member_data)

            # Add social media links
            TeamMemberSocialMedia.objects.create(team_member=member, social_media=linkedin_link)
            TeamMemberSocialMedia.objects.create(team_member=member, social_media=facebook_link)

    def create_products(self):
        """Create products"""
        self.stdout.write('Creating products...')

        paintings_category = Category.objects.get(name='Paintings')
        clay_category = Category.objects.get(name='Clay Crafts')
        textiles_category = Category.objects.get(name='Textiles')

        products_data = [
            {
                'name': 'Traditional Mithila Painting - Madhubani Art',
                'slug': 'traditional-mithila-painting-madhubani-art',
                'description': 'Authentic hand-painted Mithila artwork featuring traditional motifs and vibrant colors on handmade paper.',
                'category': paintings_category,
                'price': 2500,
                'images': [
                    'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
                    'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg'
                ],
                'dimensions': '16" x 12"',
                'materials': ['Handmade Paper', 'Natural Pigments', 'Traditional Brushes'],
                'featured': True,
                'detailed_description': 'This exquisite Madhubani painting showcases the traditional art form of Mithila region...',
                'cultural_significance': 'Madhubani paintings have been practiced for centuries in the Mithila region...'
            },
            {
                'name': 'Contemporary Mithila Fish Motif',
                'slug': 'contemporary-mithila-fish-motif',
                'description': 'Modern interpretation of traditional fish motifs, symbolizing prosperity and good fortune.',
                'category': paintings_category,
                'price': 1800,
                'images': [
                    'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg'
                ],
                'dimensions': '14" x 10"',
                'materials': ['Canvas', 'Acrylic Paints', 'Gold Leaf'],
                'featured': True
            },
            {
                'name': 'Handcrafted Clay Elephant',
                'slug': 'handcrafted-clay-elephant',
                'description': 'Beautiful clay elephant sculpture with traditional Mithila patterns and designs.',
                'category': clay_category,
                'price': 1200,
                'images': [
                    'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg'
                ],
                'dimensions': '8" x 6" x 4"',
                'materials': ['Natural Clay', 'Natural Pigments', 'Protective Coating'],
                'featured': False
            },
            {
                'name': 'Mithila Art Textile Wall Hanging',
                'slug': 'mithila-art-textile-wall-hanging',
                'description': 'Hand-painted textile featuring traditional Mithila designs, perfect for home decoration.',
                'category': textiles_category,
                'price': 950,
                'images': [
                    'https://i.etsystatic.com/43638819/r/il/7b65fd/5930357750/il_794xN.5930357750_3qzn.jpg'
                ],
                'dimensions': '24" x 18"',
                'materials': ['Cotton Fabric', 'Fabric Paints', 'Natural Dyes'],
                'featured': True
            }
        ]

        artists = list(Artist.objects.all())

        for i, product_data in enumerate(products_data):
            product = Product.objects.create(**product_data)

            # Assign artists to products
            artist = artists[i % len(artists)]
            ProductArtist.objects.create(product=product, artist=artist)

    def create_gallery_images(self):
        """Create gallery images"""
        self.stdout.write('Creating gallery images...')

        gallery_data = [
            {
                'src': 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
                'alt': 'Traditional Madhubani Fish Painting',
                'title': 'Madhubani Fish',
                'artist': 'Sarita Devi',
                'category': 'Traditional',
                'description': 'Traditional Madhubani painting featuring fish motifs, symbolizing prosperity and good fortune in Mithila culture.'
            },
            {
                'src': 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
                'alt': 'Contemporary Mithila Art',
                'title': 'Modern Mithila',
                'artist': 'Rajesh Kumar',
                'category': 'Contemporary',
                'description': 'Contemporary interpretation of traditional Mithila art with modern color palette and techniques.'
            },
            {
                'src': 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
                'alt': 'Religious Mithila Painting',
                'title': 'Divine Motifs',
                'artist': 'Kamala Devi',
                'category': 'Religious',
                'description': 'Sacred religious motifs depicting Hindu deities in traditional Mithila style.'
            },
            {
                'src': 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
                'alt': 'Nature Inspired Mithila Art',
                'title': 'Nature\'s Beauty',
                'artist': 'Sunita Sharma',
                'category': 'Nature & Wildlife',
                'description': 'Beautiful depiction of nature and wildlife in traditional Mithila painting style.'
            }
        ]

        for image_data in gallery_data:
            GalleryImage.objects.create(**image_data)

    def create_faq(self):
        """Create FAQ items"""
        self.stdout.write('Creating FAQ items...')

        faq_data = [
            {
                'question': 'What are your opening hours?',
                'answer': 'We are open daily from 9:00 AM to 8:00 PM, including holidays.',
                'order': 1
            },
            {
                'question': 'Do you offer guided tours?',
                'answer': 'Yes, we offer guided tours of our gallery and cultural center. Please contact us in advance to schedule a tour.',
                'order': 2
            },
            {
                'question': 'How can I purchase Mithila art?',
                'answer': 'You can purchase art directly from our gallery or through our online shop. We ship worldwide and offer secure payment options.',
                'order': 3
            },
            {
                'question': 'Do you host cultural events?',
                'answer': 'Yes, we regularly host cultural events, workshops, and exhibitions. Check our Events page or contact us for upcoming events.',
                'order': 4
            },
            {
                'question': 'Can I learn Mithila art at your center?',
                'answer': 'Absolutely! We offer workshops and training programs for all skill levels, from beginners to advanced artists.',
                'order': 5
            },
            {
                'question': 'Do you ship internationally?',
                'answer': 'Yes, we ship our artwork and crafts worldwide. Shipping costs and delivery times vary by location.',
                'order': 6
            }
        ]

        for faq_item in faq_data:
            FAQ.objects.create(**faq_item)

    def create_contact_info(self):
        """Create contact information"""
        self.stdout.write('Creating contact information...')

        ContactInfo.objects.create(
            phone_numbers=['+977-9814830580', '+977-9821762884'],
            email='<EMAIL>',
            address='Barahbigha, Janaki Mandir Marg, Janakpurdham-08, Dhanusha, Nepal',
            hours='Daily 9:00 AM to 8:00 PM'
        )

    def create_testimonials(self):
        """Create testimonials"""
        self.stdout.write('Creating testimonials...')

        testimonials_data = [
            {
                'name': 'Rajesh Sharma',
                'role': 'Art Collector',
                'image': 'https://randomuser.me/api/portraits/men/32.jpg',
                'quote': 'The artwork I purchased from Mithilani Ghar is absolutely stunning. The attention to detail and vibrant colors truly capture the essence of Mithila art tradition.',
                'order': 1
            },
            {
                'name': 'Sarah Johnson',
                'role': 'Tourist',
                'image': 'https://randomuser.me/api/portraits/women/44.jpg',
                'quote': 'Visiting Mithilani Ghar was the highlight of my trip to Janakpur. The artists were so welcoming and I learned so much about the cultural significance behind each piece.',
                'order': 2
            },
            {
                'name': 'Dr. Priya Patel',
                'role': 'Art Historian',
                'image': 'https://randomuser.me/api/portraits/women/68.jpg',
                'quote': 'Mithilani Ghar is doing exceptional work in preserving traditional Mithila art. Their commitment to authenticity while embracing contemporary interpretations is commendable.',
                'order': 3
            }
        ]

        for testimonial_data in testimonials_data:
            Testimonial.objects.create(**testimonial_data)

    def create_events(self):
        """Create events"""
        self.stdout.write('Creating events...')

        events_data = [
            {
                'title': 'Mithila Art Workshop',
                'date': 'June 15, 2025',
                'time': '10:00 AM - 2:00 PM',
                'location': 'Mithilani Ghar, Janakpur',
                'image_url': 'https://i.etsystatic.com/43638819/r/il/7b65fd/5930357750/il_794xN.5930357750_3qzn.jpg',
                'description': 'Learn the basics of traditional Mithila painting techniques from master artists.'
            },
            {
                'title': 'Exhibition: Modern Mithila',
                'date': 'July 5-15, 2025',
                'time': '9:00 AM - 8:00 PM',
                'location': 'Mithilani Ghar Gallery',
                'image_url': 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
                'description': 'A special exhibition showcasing contemporary interpretations of traditional Mithila art.'
            },
            {
                'title': 'Cultural Festival',
                'date': 'August 20, 2025',
                'time': '6:00 PM - 10:00 PM',
                'location': 'Mithilani Ghar Courtyard',
                'image_url': 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
                'description': 'Join us for an evening of traditional music, dance, and art demonstrations celebrating Mithila culture.'
            }
        ]

        for event_data in events_data:
            Event.objects.create(**event_data)

    def create_company_info(self):
        """Create company information and features"""
        self.stdout.write('Creating company information...')

        # Create company info
        CompanyInfo.objects.create(
            name='Mithilani Ghar',
            tagline='Preserving and Promoting the Rich Artistic Heritage of Mithila',
            description='Mithilani Ghar is a dedicated hub for promoting and preserving the rich artistic heritage of the Mithila region. Located in Janakpur, Nepal, we serve as an art gallery, craft store, and training center focused on traditional Mithila art forms.',
            mission='Our mission is to support local artists, provide authentic Mithila artwork to art enthusiasts worldwide, and ensure this unique cultural tradition continues to thrive for generations to come.',
            hero_image='https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg'
        )

        # Create company features
        features_data = [
            {
                'title': 'Art Gallery',
                'description': 'Showcasing authentic Mithila artwork',
                'icon': 'M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z',
                'color': 'blue',
                'order': 1
            },
            {
                'title': 'Training Center',
                'description': 'Learn traditional art techniques',
                'icon': 'M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253',
                'color': 'green',
                'order': 2
            },
            {
                'title': 'Craft Store',
                'description': 'Authentic handmade crafts and artwork',
                'icon': 'M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 7a2 2 0 01-2 2H8a2 2 0 01-2-2L5 9z',
                'color': 'purple',
                'order': 3
            },
            {
                'title': 'Cultural Hub',
                'description': 'Celebrating the heritage of Mithila region',
                'icon': 'M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
                'color': 'red',
                'order': 4
            }
        ]

        for feature_data in features_data:
            CompanyFeature.objects.create(**feature_data)

    def create_about_us_data(self):
        """Create About Us data"""
        self.stdout.write('Creating About Us data...')

        # Create About Us Story
        AboutUsStory.objects.create(
            title='Our Story',
            content=[
                'Mithilani Ghar was born from a deep passion for preserving the rich artistic heritage of the Mithila region. Founded in 2015 by master artist Sarita Devi, our journey began in a small village near Janakpur, where traditional Mithila art has been practiced for centuries.',
                'What started as a humble initiative to support local artists has grown into a thriving cultural center that bridges the gap between traditional art forms and contemporary appreciation. We have become a beacon for artists, art enthusiasts, and cultural preservationists from around the world.',
                'Today, Mithilani Ghar stands as a testament to the enduring beauty and cultural significance of Mithila art, continuing to inspire new generations while honoring the wisdom of our ancestors.'
            ],
            image='https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg'
        )

        # Create About Us Mission
        AboutUsMission.objects.create(
            title='Our Mission & Values',
            content='To preserve, promote, and perpetuate the rich artistic heritage of Mithila through authentic art creation, education, and cultural exchange while supporting local artists and their communities.',
            values=[
                'Authenticity - We maintain the traditional techniques and cultural significance of Mithila art',
                'Community - We support local artists and foster a sense of belonging and shared purpose',
                'Education - We share knowledge and skills to ensure the art form continues to thrive',
                'Innovation - We embrace contemporary interpretations while respecting traditional foundations',
                'Sustainability - We promote eco-friendly practices and sustainable livelihoods for artists'
            ]
        )

        # Create Achievements
        achievements_data = [
            {
                'year': '2015',
                'title': 'Foundation of Mithilani Ghar',
                'description': 'Established as a small gallery and workshop space in Janakpur',
                'order': 1
            },
            {
                'year': '2017',
                'title': 'First International Exhibition',
                'description': 'Showcased Mithila art at the Cultural Heritage Festival in New Delhi',
                'order': 2
            },
            {
                'year': '2018',
                'title': 'UNESCO Recognition',
                'description': 'Received recognition for efforts in preserving traditional art forms',
                'order': 3
            },
            {
                'year': '2020',
                'title': 'Digital Platform Launch',
                'description': 'Launched online gallery and virtual workshops during the pandemic',
                'order': 4
            },
            {
                'year': '2022',
                'title': 'Artist Training Program',
                'description': 'Established comprehensive training program for aspiring Mithila artists',
                'order': 5
            }
        ]

        for achievement_data in achievements_data:
            Achievement.objects.create(**achievement_data)

        # Create Facilities
        facilities_data = [
            {
                'name': 'Main Gallery',
                'description': 'A spacious exhibition hall showcasing rotating displays of traditional and contemporary Mithila art.',
                'image': 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
                'features': [
                    'Climate-controlled environment',
                    'Professional lighting system',
                    'Interactive display areas',
                    'Guided tour facilities'
                ],
                'order': 1
            },
            {
                'name': 'Artist Studios',
                'description': 'Dedicated workspace for resident and visiting artists to create their masterpieces.',
                'image': 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
                'features': [
                    'Natural lighting',
                    'Traditional art supplies',
                    'Collaborative workspace',
                    'Storage facilities'
                ],
                'order': 2
            },
            {
                'name': 'Workshop Center',
                'description': 'Educational facility equipped for teaching traditional Mithila art techniques to students of all ages.',
                'image': 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
                'features': [
                    'Flexible seating arrangements',
                    'Audio-visual equipment',
                    'Art supply stations',
                    'Display boards'
                ],
                'order': 3
            },
            {
                'name': 'Cultural Library',
                'description': 'A repository of books, documents, and digital archives related to Mithila culture and art history.',
                'image': 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
                'features': [
                    'Historical documents',
                    'Digital archives',
                    'Research facilities',
                    'Reading areas'
                ],
                'order': 4
            }
        ]

        for facility_data in facilities_data:
            Facility.objects.create(**facility_data)
