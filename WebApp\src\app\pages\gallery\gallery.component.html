<!-- <PERSON> Banner -->
<div class="relative h-[50vh] overflow-hidden">
  <!-- Background Image -->
  <div class="absolute inset-0 bg-cover bg-center bg-no-repeat"
       style="background-image: url('https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg')">
  </div>

  <!-- Gradient Overlay -->
  <div class="absolute inset-0 bg-gradient-to-br from-primary-800/90 via-primary-700/90 to-brick-700/90"></div>

  <!-- <PERSON><PERSON><PERSON> Background -->
  <div class="absolute inset-0 opacity-10">
    <app-mithila-art-background
      [primaryColor]="'#C1440E'"
      [secondaryColor]="'#F4B400'"
      opacity="15">
    </app-mithila-art-background>
  </div>

  <!-- Content -->
  <div class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10">
    <!-- Decorative Element -->
    <app-mithila-decorative-element
      [primaryColor]="'#C1440E'"
      [secondaryColor]="'#F4B400'"
      [type]="'lotus'"
      position="relative mb-6"
      classes="opacity-90"
      size="80px">
    </app-mithila-decorative-element>

    <!-- Title -->
    <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4">
      Art Gallery
    </h1>

    <!-- Subtitle -->
    <p class="text-lg sm:text-xl md:text-2xl text-white max-w-3xl mx-auto">
      Explore our collection of authentic Mithila artwork
    </p>
  </div>
</div>

<!-- Gallery Section -->
<app-mithila-section
  primaryColor="#F4B400"
  secondaryColor="#264653"
  backgroundGradient="from-primary-50 via-background-light to-secondary-50"
  backgroundOpacity="10"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Photo Gallery"
    subtitle="Browse our collection of beautiful Mithila art"
  ></app-section-title>

  <!-- Category Filter -->
  <div class="flex flex-wrap justify-center gap-3 mt-8 mb-12">
    <button
      *ngFor="let category of categories"
      (click)="selectCategory(category)"
      class="px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 shadow-md"
      [ngClass]="selectedCategory === category ? 'bg-primary-600 text-white' : 'bg-white/80 text-gray-700 hover:bg-primary-100'">
      {{category}}
    </button>
  </div>

  <!-- Photo Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
    <div *ngFor="let image of filteredImages; let i = index"
         class="group relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer"
         (click)="openLightbox(i)">

      <!-- Image -->
      <div class="relative overflow-hidden aspect-square">
        <img [src]="image.src" [alt]="image.alt"
             class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">

        <!-- Overlay with Info -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <!-- Image Info on Hover -->
          <div class="absolute bottom-0 left-0 right-0 p-4 text-white">
            <h3 class="font-bold text-lg mb-1">{{image.title}}</h3>
            <p class="text-sm opacity-90 mb-1">By {{image.artist}}</p>
            <p class="text-xs text-yellow-300 font-medium">{{image.category}}</p>
          </div>
        </div>

        <!-- View Icon -->
        <div class="absolute top-4 right-4 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          </svg>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Lightbox Component -->
<app-lightbox
  [images]="filteredImages"
  [currentIndex]="currentImageIndex"
  [isOpen]="isLightboxOpen"
  (close)="closeLightbox()"
  (indexChange)="onImageIndexChange($event)">
</app-lightbox>




