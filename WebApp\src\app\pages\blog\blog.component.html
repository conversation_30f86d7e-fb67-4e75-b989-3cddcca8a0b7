<!-- Enhanced <PERSON> Banner with <PERSON><PERSON>la Art Elements -->
<div class="relative h-[60vh] md:h-[70vh] overflow-hidden">
  <!-- Background Image with Parallax Effect -->
  <div
    class="absolute inset-0 bg-cover bg-center bg-no-repeat transform transition-transform duration-10000 hover:scale-105"
    style="
      background-image: url('https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg');
    "
  ></div>

  <!-- Overlay Gradient -->
  <div
    class="absolute inset-0 bg-gradient-to-b from-gray-900/60 via-gray-900/40 to-gray-900/70"
  ></div>

  <!-- Decorative Art Background -->
  <app-mithila-art-background
    [primaryColor]="'#C1440E'"
    [secondaryColor]="'#F4B400'"
    [opacity]="'0.15'"
  ></app-mithila-art-background>

  <!-- Content -->
  <div
    class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10"
  >
    <!-- Decorative Element -->
    <app-mithila-decorative-element
      [primaryColor]="'#C1440E'"
      [secondaryColor]="'#F4B400'"
      [type]="'lotus'"
      position="relative mb-6"
      classes="opacity-90 animate-float-slow"
      size="80px"
    >
    </app-mithila-decorative-element>

    <!-- Title with Animation -->
    <h1
      class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 font-heading animate-fade-in"
    >
      Mithila Art Blog
    </h1>

    <!-- Subtitle -->
    <p
      class="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto mb-8 animate-fade-in delay-300"
    >
      Stories, insights, and news about the rich tradition of Mithila art and
      culture
    </p>

    <!-- Search Bar -->
    <div class="w-full max-w-2xl mx-auto mt-4 animate-fade-in delay-500">
      <div class="relative">
        <input
          type="text"
          [(ngModel)]="searchQuery"
          placeholder="Search articles..."
          class="w-full px-6 py-3 rounded-full bg-white/90 backdrop-blur-sm text-gray-800 focus:outline-none focus:ring-2 focus:ring-primary-500 shadow-lg"
        />
        <button
          class="absolute right-3 top-1/2 transform -translate-y-1/2 text-primary-600 hover:text-primary-800 transition-colors duration-300"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Decorative Border -->
  <app-mithila-border
    [primaryColor]="'#C1440E'"
    [secondaryColor]="'#F4B400'"
    [position]="'bottom'"
    [height]="'30px'"
  ></app-mithila-border>
</div>

<!-- Blog Categories Section -->
<app-mithila-section
  primaryColor="#C1440E"
  secondaryColor="#F4B400"
  backgroundGradient="from-primary-50 via-background-light to-secondary-50"
  backgroundOpacity="10"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true"
>
  <app-section-title
    title="Explore by Category"
    subtitle="Discover articles on various aspects of Mithila art and culture"
  ></app-section-title>

  <div
    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-12"
    @staggerIn
  >
    <div
      *ngFor="let category of categories; let i = index"
      class="group relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer"
      (click)="setActiveCategory(category.id)"
      @fadeIn
    >
      <!-- Decorative Border -->
      <div
        class="absolute -inset-1 bg-gradient-to-br rounded-lg blur-sm opacity-30 group-hover:opacity-100 transition-opacity duration-300"
        [ngStyle]="{
          'background-image':
            'linear-gradient(to bottom right, ' +
            category.color +
            '80, ' +
            category.color +
            '40, ' +
            category.color +
            '80)'
        }"
      ></div>

      <div class="relative rounded-lg overflow-hidden">
        <!-- Image -->
        <div class="relative h-64 overflow-hidden">
          <img
            [src]="category.image"
            [alt]="category.name"
            class="w-full h-full object-cover object-center transition-transform duration-700 group-hover:scale-110"
          />

          <!-- Overlay Gradient -->
          <div
            class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300"
          ></div>

          <!-- Category Name -->
          <div class="absolute inset-0 flex flex-col justify-end p-6">
            <h3 class="text-2xl font-bold text-white mb-2 drop-shadow-md">
              {{ category.name }}
            </h3>
            <p class="text-white/90 text-sm mb-4 line-clamp-2 drop-shadow-md">
              {{ category.description }}
            </p>

            <!-- Article Count Badge -->
            <div
              class="inline-flex items-center justify-center px-3 py-1 rounded-full bg-white/20 backdrop-blur-sm text-white text-sm font-medium mb-2 w-fit"
            >
              {{ category.count }} Articles
            </div>
          </div>
        </div>

        <!-- Button -->
        <div class="p-4 bg-white/90 backdrop-blur-sm">
          <button
            class="w-full py-3 rounded-full font-medium transition-all duration-300 flex items-center justify-center"
            [ngClass]="
              activeCategory === category.id
                ? 'bg-opacity-100 text-white'
                : 'bg-opacity-10 hover:bg-opacity-20 text-gray-800 hover:text-gray-900'
            "
            [ngStyle]="{
              'background-color':
                activeCategory === category.id
                  ? category.color
                  : category.color + '20'
            }"
          >
            Browse {{ category.name }}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 ml-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M14 5l7 7m0 0l-7 7m7-7H3"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Featured Articles Section -->
<app-mithila-section
  primaryColor="#F4B400"
  secondaryColor="#264653"
  backgroundGradient="from-secondary-50 via-background-light to-accent-50"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true"
>
  <app-section-title
    title="Featured Articles"
    subtitle="Handpicked stories and insights about Mithila art and culture"
  ></app-section-title>

  <!-- Featured Articles Grid -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-12" @staggerIn>
    <div
      *ngFor="let post of featuredPosts; let i = index"
      class="bg-white/80 backdrop-blur-sm rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group"
      @fadeIn
    >
      <div class="relative overflow-hidden h-64">
        <!-- Article Image -->
        <img
          [src]="post.image"
          [alt]="post.title"
          class="w-full h-full object-cover object-center transition-transform duration-700 group-hover:scale-110"
        />

        <!-- Overlay Gradient -->
        <div
          class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300"
        ></div>

        <!-- Featured Badge -->
        <div class="absolute top-4 left-4">
          <span
            class="inline-block px-3 py-1 bg-secondary-500/80 backdrop-blur-sm text-white rounded-full text-xs font-bold"
          >
            Featured
          </span>
        </div>

        <!-- Category Badge -->
        <div class="absolute top-4 right-4">
          <span
            class="inline-block px-3 py-1 bg-white/80 backdrop-blur-sm text-gray-800 rounded-full text-xs font-bold"
          >
            {{
              post.category === "art-techniques"
                ? "Art Techniques"
                : post.category === "cultural-insights"
                ? "Cultural Insights"
                : post.category === "artist-stories"
                ? "Artist Stories"
                : "Events & News"
            }}
          </span>
        </div>

        <!-- Article Title -->
        <div class="absolute bottom-0 left-0 right-0 p-4">
          <h3 class="text-xl font-bold text-white drop-shadow-md line-clamp-2">
            {{ post.title }}
          </h3>
        </div>
      </div>

      <div class="p-6 relative">
        <!-- Author and Date -->
        <div class="flex items-center gap-3 mb-3">
          <img
            [src]="post.authorImage"
            [alt]="post.author"
            class="w-10 h-10 rounded-full object-cover"
          />
          <div>
            <p class="font-medium text-gray-900">{{ post.author }}</p>
            <p class="text-sm text-gray-600">
              {{ post.date }} · {{ post.readTime }}
            </p>
          </div>
        </div>

        <!-- Excerpt -->
        <p class="text-gray-700 line-clamp-3 mb-4">{{ post.excerpt }}</p>

        <!-- Tags -->
        <div class="flex flex-wrap gap-2 mb-4">
          <span
            *ngFor="let tag of post.tags"
            class="inline-block px-2 py-1 bg-secondary-100 text-secondary-600 rounded-full text-xs font-medium"
          >
            #{{ tag }}
          </span>
        </div>

        <!-- Read More Button -->
        <a
          [routerLink]="['/blog', post.id]"
          class="inline-flex items-center px-5 py-2 bg-secondary-100 text-secondary-600 rounded-full font-medium hover:bg-secondary-200 transition-colors duration-300"
        >
          Read Article
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4 ml-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M14 5l7 7m0 0l-7 7m7-7H3"
            />
          </svg>
        </a>

        <!-- Decorative Element -->
        <app-mithila-decorative-element
          [primaryColor]="'#F4B400'"
          [secondaryColor]="'#264653'"
          [type]="i === 0 ? 'lotus' : i === 1 ? 'fish' : 'peacock'"
          position="absolute -top-6 -right-6"
          classes="opacity-10 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none"
          size="30px"
        >
        </app-mithila-decorative-element>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Recent Articles Section -->
<app-mithila-section
  primaryColor="#264653"
  secondaryColor="#3B945E"
  backgroundGradient="from-accent-50 via-background-light to-success-50"
  backgroundOpacity="10"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true"
>
  <app-section-title
    title="Recent Articles"
    subtitle="Stay updated with our latest stories and insights"
  ></app-section-title>

  <!-- Filter Buttons -->
  <div class="flex flex-wrap justify-center gap-3 mt-8 mb-12">
    <button
      (click)="setActiveCategory('all')"
      class="px-5 py-2 rounded-full font-medium transition-all duration-300"
      [ngClass]="
        activeCategory === 'all'
          ? 'bg-accent-600 text-white'
          : 'bg-accent-100 text-accent-600 hover:bg-accent-200'
      "
    >
      All Categories
    </button>
    <button
      *ngFor="let category of categories"
      (click)="setActiveCategory(category.id)"
      class="px-5 py-2 rounded-full font-medium transition-all duration-300"
      [ngClass]="
        activeCategory === category.id
          ? 'bg-accent-600 text-white'
          : 'bg-accent-100 text-accent-600 hover:bg-accent-200'
      "
    >
      {{ category.name }}
    </button>
  </div>

  <!-- Articles Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" @staggerIn>
    <div
      *ngFor="let post of filteredPosts; let i = index"
      class="bg-white/80 backdrop-blur-sm rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group"
      @fadeIn
    >
      <div class="relative overflow-hidden h-48">
        <!-- Article Image -->
        <img
          [src]="post.image"
          [alt]="post.title"
          class="w-full h-full object-cover object-center transition-transform duration-700 group-hover:scale-110"
        />

        <!-- Overlay Gradient -->
        <div
          class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300"
        ></div>

        <!-- Category Badge -->
        <div class="absolute top-4 right-4">
          <span
            class="inline-block px-3 py-1 bg-white/80 backdrop-blur-sm text-gray-800 rounded-full text-xs font-bold"
          >
            {{
              post.category === "art-techniques"
                ? "Art Techniques"
                : post.category === "cultural-insights"
                ? "Cultural Insights"
                : post.category === "artist-stories"
                ? "Artist Stories"
                : "Events & News"
            }}
          </span>
        </div>

        <!-- Article Title -->
        <div class="absolute bottom-0 left-0 right-0 p-4">
          <h3 class="text-lg font-bold text-white drop-shadow-md line-clamp-2">
            {{ post.title }}
          </h3>
        </div>
      </div>

      <div class="p-4 relative">
        <!-- Author and Date -->
        <div class="flex items-center gap-2 mb-2">
          <img
            [src]="post.authorImage"
            [alt]="post.author"
            class="w-8 h-8 rounded-full object-cover"
          />
          <div>
            <p class="font-medium text-sm text-gray-900">{{ post.author }}</p>
            <p class="text-xs text-gray-600">
              {{ post.date }} · {{ post.readTime }}
            </p>
          </div>
        </div>

        <!-- Excerpt -->
        <p class="text-gray-700 text-sm line-clamp-2 mb-3">
          {{ post.excerpt }}
        </p>

        <!-- Read More Link -->
        <a
          [routerLink]="['/blog', post.id]"
          class="inline-flex items-center text-sm text-accent-600 hover:text-accent-800 transition-colors duration-300"
        >
          Read More
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4 ml-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M14 5l7 7m0 0l-7 7m7-7H3"
            />
          </svg>
        </a>

        <!-- Decorative Element -->
        <app-mithila-decorative-element
          [primaryColor]="'#264653'"
          [secondaryColor]="'#3B945E'"
          [type]="i % 3 === 0 ? 'lotus' : i % 3 === 1 ? 'fish' : 'peacock'"
          position="absolute -top-6 -right-6"
          classes="opacity-10 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none"
          size="20px"
        >
        </app-mithila-decorative-element>
      </div>
    </div>
  </div>

  <!-- Load More Button -->
  <div class="flex justify-center mt-12">
    <button
      class="px-6 py-3 bg-accent-600 hover:bg-accent-700 text-white font-medium rounded-full transition-colors duration-300 flex items-center shadow-md"
    >
      Load More Articles
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5 ml-2"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M19 14l-7 7m0 0l-7-7m7 7V3"
        />
      </svg>
    </button>
  </div>
</app-mithila-section>

<!-- Popular Tags Section -->
<app-mithila-section
  primaryColor="#E76F51"
  secondaryColor="#C1440E"
  backgroundGradient="from-brick-50 via-background-light to-primary-50"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true"
>
  <app-section-title
    title="Popular Topics"
    subtitle="Explore articles by popular tags and topics"
  ></app-section-title>

  <div class="flex flex-wrap justify-center gap-3 mt-8">
    <div *ngFor="let tag of popularTags" class="group relative">
      <!-- Decorative Border -->
      <div
        class="absolute -inset-1 bg-gradient-to-br from-brick-300/40 to-primary-300/40 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"
      ></div>

      <button
        class="relative px-5 py-2 bg-white/80 backdrop-blur-sm text-gray-800 rounded-full font-medium hover:bg-brick-50 transition-colors duration-300 shadow-sm flex items-center gap-2"
      >
        #{{ tag.name }}
        <span
          class="inline-block px-2 py-0.5 bg-brick-100 text-brick-600 rounded-full text-xs"
          >{{ tag.count }}</span
        >
      </button>
    </div>
  </div>
</app-mithila-section>

<!-- Simple Newsletter Section -->
<div class="py-16 bg-gray-50">
  <div class="container mx-auto px-4">
    <div
      class="max-w-3xl mx-auto bg-white rounded-lg shadow-md overflow-hidden"
    >
      <!-- Simple Decorative Element -->
      <div class="flex justify-center mt-8">
        <app-mithila-decorative-element
          [primaryColor]="'#008C8C'"
          [secondaryColor]="'#D81B60'"
          [type]="'lotus'"
          position="relative"
          classes="opacity-90"
          size="60px"
        >
        </app-mithila-decorative-element>
      </div>

      <!-- Content -->
      <div class="p-8 text-center">
        <h2 class="text-2xl font-bold mb-6 text-gray-800">
          Subscribe to Our Newsletter
        </h2>

        <p class="mb-8 text-gray-700 text-lg max-w-2xl mx-auto">
          Stay updated with the latest articles, events, and insights about
          Mithila art and culture. Join our community of art enthusiasts.
        </p>

        <!-- Simple Subscription Form -->
        <div class="max-w-md mx-auto mb-6">
          <div class="flex flex-col sm:flex-row gap-3">
            <input
              type="email"
              placeholder="Your email address"
              class="flex-grow px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-peacock-500 focus:border-transparent"
            />
            <button
              class="px-6 py-3 text-black hover:bg-peacock-700 text-white font-medium rounded-lg transition-colors duration-300 flex items-center justify-center"
            >
              Subscribe
            </button>
          </div>
        </div>

        <!-- Benefits List -->
        <div class="flex flex-wrap justify-center gap-8 mb-6">
          <div class="flex items-center gap-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 text-peacock-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span class="text-gray-700">Exclusive art insights</span>
          </div>
          <div class="flex items-center gap-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 text-peacock-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span class="text-gray-700">Event notifications</span>
          </div>
          <div class="flex items-center gap-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 text-peacock-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span class="text-gray-700">Special offers</span>
          </div>
        </div>

        <!-- Privacy Note -->
        <div class="flex items-center justify-center gap-2 text-gray-600">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
            />
          </svg>
          <span class="text-sm"
            >We respect your privacy. Unsubscribe at any time.</span
          >
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Contact Us Section -->
<app-mithila-section
  primaryColor="#008C8C"
  secondaryColor="#D81B60"
  backgroundGradient="from-peacock-50 via-background-light to-magenta-50"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true"
>
  <app-section-title
    title="Contact Us"
    subtitle="We'd love to hear from you. Reach out with any questions or inquiries."
  ></app-section-title>

  <div class="max-w-6xl mx-auto mt-12">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Contact Information -->
      <div class="lg:col-span-1">
        <div
          class="bg-white/90 backdrop-blur-sm rounded-lg shadow-md p-6 h-full"
        >
          <h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6 text-peacock-600 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            Contact Information
          </h3>

          <div class="space-y-6">
            <!-- Address -->
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <div class="bg-peacock-100 p-3 rounded-full">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-peacock-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    />
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <h4 class="text-md font-semibold text-gray-800">
                  Our Location
                </h4>
                <p class="text-gray-600 mt-1">
                  123 Mithila Art Street, Madhubani, Bihar, India
                </p>
              </div>
            </div>

            <!-- Email -->
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <div class="bg-peacock-100 p-3 rounded-full">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-peacock-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <h4 class="text-md font-semibold text-gray-800">Email Us</h4>
                <p class="text-gray-600 mt-1">info&#64;mithilaghar.com</p>
                <p class="text-gray-600">support&#64;mithilaghar.com</p>
              </div>
            </div>

            <!-- Phone -->
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <div class="bg-peacock-100 p-3 rounded-full">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-peacock-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    />
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <h4 class="text-md font-semibold text-gray-800">Call Us</h4>
                <p class="text-gray-600 mt-1">+91 98765 43210</p>
                <p class="text-gray-600">+91 12345 67890</p>
              </div>
            </div>

            <!-- Hours -->
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <div class="bg-peacock-100 p-3 rounded-full">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-peacock-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <h4 class="text-md font-semibold text-gray-800">
                  Business Hours
                </h4>
                <p class="text-gray-600 mt-1">
                  Monday - Friday: 9:00 AM - 6:00 PM
                </p>
                <p class="text-gray-600">Saturday: 10:00 AM - 4:00 PM</p>
                <p class="text-gray-600">Sunday: Closed</p>
              </div>
            </div>
          </div>

          <!-- Social Media Links -->
          <div class="mt-8">
            <h4 class="text-md font-semibold text-gray-800 mb-4">
              Connect With Us
            </h4>
            <div class="flex space-x-4">
              <a
                href="#"
                class="bg-peacock-100 p-3 rounded-full hover:bg-peacock-200 transition-colors duration-300"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 text-peacock-600"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                  />
                </svg>
              </a>
              <a
                href="#"
                class="bg-peacock-100 p-3 rounded-full hover:bg-peacock-200 transition-colors duration-300"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 text-peacock-600"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z"
                  />
                </svg>
              </a>
              <a
                href="#"
                class="bg-peacock-100 p-3 rounded-full hover:bg-peacock-200 transition-colors duration-300"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 text-peacock-600"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"
                  />
                </svg>
              </a>
              <a
                href="#"
                class="bg-peacock-100 p-3 rounded-full hover:bg-peacock-200 transition-colors duration-300"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 text-peacock-600"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"
                  />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Contact Form -->
      <div class="lg:col-span-2">
        <div
          class="bg-white/90 backdrop-blur-sm rounded-lg shadow-md p-6 h-full"
        >
          <h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6 text-magenta-600 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
              />
            </svg>
            Send Us a Message
          </h3>

          <form class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Name -->
              <div>
                <label
                  for="name"
                  class="block text-sm font-medium text-gray-700 mb-1"
                  >Full Name</label
                >
                <input
                  type="text"
                  id="name"
                  name="name"
                  placeholder="Your full name"
                  class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-magenta-500 focus:border-transparent"
                />
              </div>

              <!-- Email -->
              <div>
                <label
                  for="email"
                  class="block text-sm font-medium text-gray-700 mb-1"
                  >Email Address</label
                >
                <input
                  type="email"
                  id="email"
                  name="email"
                  placeholder="Your email address"
                  class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-magenta-500 focus:border-transparent"
                />
              </div>
            </div>

            <!-- Phone -->
            <div>
              <label
                for="phone"
                class="block text-sm font-medium text-gray-700 mb-1"
                >Phone Number (Optional)</label
              >
              <input
                type="tel"
                id="phone"
                name="phone"
                placeholder="Your phone number"
                class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-magenta-500 focus:border-transparent"
              />
            </div>

            <!-- Subject -->
            <div>
              <label
                for="subject"
                class="block text-sm font-medium text-gray-700 mb-1"
                >Subject</label
              >
              <input
                type="text"
                id="subject"
                name="subject"
                placeholder="What is this regarding?"
                class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-magenta-500 focus:border-transparent"
              />
            </div>

            <!-- Message -->
            <div>
              <label
                for="message"
                class="block text-sm font-medium text-gray-700 mb-1"
                >Message</label
              >
              <textarea
                id="message"
                name="message"
                rows="5"
                placeholder="Your message"
                class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-magenta-500 focus:border-transparent"
              ></textarea>
            </div>

            <!-- Checkbox -->
            <div class="flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="privacy"
                  name="privacy"
                  type="checkbox"
                  class="h-4 w-4 text-magenta-600 focus:ring-magenta-500 border-gray-300 rounded"
                />
              </div>
              <div class="ml-3 text-sm">
                <label for="privacy" class="text-gray-600"
                  >I agree to the
                  <a href="#" class="text-magenta-600 hover:text-magenta-800"
                    >Privacy Policy</a
                  >
                  and consent to being contacted regarding my inquiry.</label
                >
              </div>
            </div>

            <!-- Submit Button -->
            <div>
              <button
                type="submit"
                class="w-full px-6 py-3 bg-gradient-to-r from-peacock-500 to-magenta-500 hover:from-peacock-600 hover:to-magenta-600 text-white font-medium rounded-lg transition-colors duration-300 flex items-center justify-center shadow-md"
              >
                Send Message
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 ml-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                  />
                </svg>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Map Section -->
    <div
      class="mt-12 bg-white/90 backdrop-blur-sm rounded-lg shadow-md p-6 overflow-hidden"
    >
      <h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6 text-peacock-600 mr-2"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"
          />
        </svg>
        Find Us
      </h3>

      <!-- Placeholder for Map - In a real implementation, you would integrate Google Maps or another map service -->
      <div
        class="relative w-full h-96 rounded-lg overflow-hidden border border-gray-200"
      >
        <div
          class="absolute inset-0 bg-gray-100 flex items-center justify-center"
        >
          <div class="text-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-16 w-16 text-gray-400 mx-auto mb-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"
              />
            </svg>
            <p class="text-gray-600">Interactive map will be displayed here</p>
            <p class="text-gray-500 text-sm mt-2">
              123 Mithila Art Street, Madhubani, Bihar, India
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>
