import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-brush-stroke',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './brush-stroke.component.html',
  styleUrl: './brush-stroke.component.css'
})
export class BrushStrokeComponent {
  @Input() color: string = 'primary';
  @Input() width: string = '100%';
  @Input() height: string = '100px';
  @Input() position: 'top' | 'bottom' | 'left' | 'right' = 'bottom';
}
