<section class="py-16 md:py-24 bg-white">
  <div class="container mx-auto px-4">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
      <!-- Image -->
      <div class="relative" @slideFromLeft>
        <!-- Main Image -->
        <div class="rounded-lg overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-rotate-1 hover:scale-105 relative z-10">
          <img src="https://images.unsplash.com/photo-1625046438416-cf1a3b139c89?q=80&w=2070&auto=format&fit=crop" alt="Mithilani Ghar" class="w-full h-auto">
        </div>
        
        <!-- Decorative Border -->
        <div class="absolute -bottom-6 -right-6 w-full h-full border-4 border-dashed border-primary-200 rounded-lg z-0"></div>
      </div>
      
      <!-- Content -->
      <div @fadeIn>
        <h2 class="text-3xl md:text-4xl font-heading font-semibold text-primary-500 mb-6">Discover <PERSON><PERSON><PERSON></h2>
        
        <p class="text-lg text-gray-700 mb-6 leading-relaxed">
          Nestled in the heart of Janakpur, Mithilani Ghar offers an authentic Maithili cultural experience with modern comforts. Our boutique hotel celebrates the rich artistic heritage of the Mithila region through traditional architecture, decor, and hospitality.
        </p>
        
        <p class="text-lg text-gray-700 mb-8 leading-relaxed">
          Located just steps away from the sacred Janaki Temple, we provide the perfect base for pilgrims and tourists alike to explore the spiritual and cultural treasures of Janakpur while enjoying our warm Maithili hospitality.
        </p>
        
        <a routerLink="/about" class="btn btn-primary inline-flex items-center">
          <span>Learn More About Us</span>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </a>
      </div>
    </div>
  </div>
</section>
