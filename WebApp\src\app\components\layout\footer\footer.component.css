/* Enhanced <PERSON><PERSON> Styles with <PERSON><PERSON><PERSON> Art Inspiration */

/* Gradient animations */
@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.gradient-animated {
  background-size: 200% 200%;
  animation: gradientShift 6s ease infinite;
}

/* Social media hover effects */
.social-link {
  position: relative;
  overflow: hidden;
}

.social-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.social-link:hover::before {
  left: 100%;
}

/* Newsletter input focus effects */
.newsletter-input:focus {
  box-shadow: 0 0 0 3px rgba(193, 68, 14, 0.1);
  transform: translateY(-1px);
}

/* Link hover animations */
.footer-link {
  position: relative;
  transition: all 0.3s ease;
}

.footer-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #C1440E, #F4B400);
  transition: width 0.3s ease;
}

.footer-link:hover::after {
  width: 100%;
}

/* Contact card hover effects */
.contact-card {
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.contact-card:hover {
  border-left-color: #C1440E;
  transform: translateX(4px);
}

/* Decorative elements */
.decorative-border {
  background: linear-gradient(90deg, transparent, #C1440E, #F4B400, #C1440E, transparent);
  height: 1px;
}

/* Newsletter benefits animation */
.benefit-item {
  opacity: 0;
  transform: translateX(-10px);
  animation: slideInLeft 0.5s ease forwards;
}

.benefit-item:nth-child(1) { animation-delay: 0.1s; }
.benefit-item:nth-child(2) { animation-delay: 0.2s; }
.benefit-item:nth-child(3) { animation-delay: 0.3s; }

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Heart beat animation */
@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.heart-beat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

/* Floating animation for decorative elements */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

/* Glow effect for important elements */
.glow-effect {
  box-shadow: 0 0 20px rgba(193, 68, 14, 0.3);
  transition: box-shadow 0.3s ease;
}

.glow-effect:hover {
  box-shadow: 0 0 30px rgba(193, 68, 14, 0.5);
}

/* Enhanced Responsive Design */

/* Mobile styles */
@media (max-width: 640px) {
  .footer-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .footer-section {
    text-align: center;
    padding: 1rem;
  }

  .social-links {
    justify-content: center;
    gap: 1rem;
  }

  .social-link {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .footer-bottom {
    text-align: center;
    padding: 1rem;
  }

  .footer-bottom .flex {
    flex-direction: column;
    gap: 0.75rem;
  }

  .newsletter-input {
    width: 100%;
    margin-bottom: 0.75rem;
  }

  .newsletter-button {
    width: 100%;
  }

  .contact-card {
    padding: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .footer-link {
    padding: 0.5rem 0;
    display: block;
  }
}

/* Tablet styles */
@media (min-width: 641px) and (max-width: 1024px) {
  .footer-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .footer-section {
    padding: 1.5rem;
  }

  .social-links {
    justify-content: flex-start;
    gap: 1.25rem;
  }

  .social-link {
    width: 44px;
    height: 44px;
    font-size: 1.125rem;
  }

  .newsletter-form {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .newsletter-input {
    width: 100%;
  }

  .newsletter-button {
    width: 100%;
  }
}

/* Desktop styles */
@media (min-width: 1025px) {
  .footer-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 2.5rem;
  }

  .footer-section {
    padding: 2rem;
  }

  .social-links {
    justify-content: flex-start;
    gap: 1.5rem;
  }

  .social-link {
    width: 48px;
    height: 48px;
    font-size: 1.25rem;
  }

  .newsletter-form {
    display: flex;
    gap: 0.5rem;
  }

  .newsletter-input {
    flex: 1;
  }

  .newsletter-button {
    flex-shrink: 0;
  }

  .contact-card:hover {
    transform: translateX(8px);
  }
}

/* Large desktop styles */
@media (min-width: 1440px) {
  .footer-container {
    max-width: 1400px;
    margin: 0 auto;
  }

  .footer-section {
    padding: 2.5rem;
  }

  .social-link {
    width: 52px;
    height: 52px;
    font-size: 1.375rem;
  }
}

/* Touch-friendly improvements for mobile */
@media (max-width: 768px) and (pointer: coarse) {
  .footer-link,
  .social-link {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .newsletter-input,
  .newsletter-button {
    min-height: 44px;
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Landscape mobile adjustments */
@media (max-width: 768px) and (orientation: landscape) {
  .footer-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .footer-section {
    padding: 0.75rem;
  }
}

/* Custom scrollbar for newsletter section */
.newsletter-section::-webkit-scrollbar {
  width: 4px;
}

.newsletter-section::-webkit-scrollbar-track {
  background: rgba(193, 68, 14, 0.1);
}

.newsletter-section::-webkit-scrollbar-thumb {
  background: rgba(193, 68, 14, 0.3);
  border-radius: 2px;
}

.newsletter-section::-webkit-scrollbar-thumb:hover {
  background: rgba(193, 68, 14, 0.5);
}

/* Accessibility improvements */
.footer-link:focus,
.social-link:focus {
  outline: 2px solid #C1440E;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  footer {
    background: white !important;
    color: black !important;
  }

  .decorative-elements {
    display: none;
  }
}
