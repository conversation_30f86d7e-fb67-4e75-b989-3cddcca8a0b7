from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    SocialMediaLinkViewSet, ArtistViewSet, ArtistSpotlightViewSet, TeamMemberViewSet,
    CategoryViewSet, ProductViewSet, GalleryImageViewSet, FAQViewSet, ContactInfoViewSet,
    TestimonialViewSet, EventViewSet, CompanyFeatureViewSet, CompanyInfoViewSet,
    AboutUsStoryViewSet, AboutUsMissionViewSet, AchievementViewSet, FacilityViewSet,
    CartViewSet, OrderViewSet, ContactInquiryViewSet, ArtistApplicationViewSet,
    DataServiceViewSet
)

router = DefaultRouter()

# Register all viewsets
router.register(r'social-media-links', SocialMediaLinkViewSet)
router.register(r'artists', ArtistViewSet)
router.register(r'artist-spotlight', ArtistSpotlightViewSet)
router.register(r'team-members', TeamMemberViewSet)
router.register(r'categories', CategoryViewSet)
router.register(r'products', ProductViewSet)
router.register(r'gallery', GalleryImageViewSet)
router.register(r'faq', FAQViewSet)
router.register(r'contact-info', ContactInfoViewSet)
router.register(r'testimonials', TestimonialViewSet)
router.register(r'events', EventViewSet)
router.register(r'company-features', CompanyFeatureViewSet)
router.register(r'company-info', CompanyInfoViewSet)
router.register(r'about-us-story', AboutUsStoryViewSet)
router.register(r'about-us-mission', AboutUsMissionViewSet)
router.register(r'achievements', AchievementViewSet)
router.register(r'facilities', FacilityViewSet)
router.register(r'cart', CartViewSet)
router.register(r'orders', OrderViewSet)
router.register(r'contact-inquiries', ContactInquiryViewSet)
router.register(r'artist-applications', ArtistApplicationViewSet)
router.register(r'data-service', DataServiceViewSet, basename='data-service')

urlpatterns = [
    path('api/', include(router.urls)),
]
