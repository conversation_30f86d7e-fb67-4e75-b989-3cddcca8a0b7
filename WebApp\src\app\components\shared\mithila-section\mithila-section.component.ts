import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MithilaArtBackgroundComponent } from '../mithila-art-background/mithila-art-background.component';
import { MithilaBorderComponent } from '../mithila-border/mithila-border.component';
import { MithilaDecorativeElementComponent } from '../mithila-decorative-element/mithila-decorative-element.component';

@Component({
  selector: 'app-mithila-section',
  standalone: true,
  imports: [
    CommonModule,
    MithilaArtBackgroundComponent,
    MithilaBorderComponent,
    MithilaDecorativeElementComponent
  ],
  template: `
    <section class="relative overflow-hidden {{padding}} {{classes}} mithila-floating-section">
      <!-- Enhanced Background with Multiple Layers -->
      <div class="absolute inset-0 overflow-hidden">
        <!-- Primary Gradient Background -->
        <div class="absolute inset-0 bg-gradient-to-bl {{backgroundGradient}} animate-gradient-background opacity-70"></div>

        <!-- Secondary Animated Gradient -->
        <div class="absolute inset-0 bg-gradient-to-tr from-transparent via-white/5 to-transparent animate-gradient-shift"></div>

        <!-- Floating Gradient Orbs -->
        <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-radial from-{{primaryColor}}/10 to-transparent rounded-full blur-3xl animate-float-slow"></div>
        <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-radial from-{{secondaryColor}}/10 to-transparent rounded-full blur-3xl animate-float-medium"></div>
        <div class="absolute top-1/2 right-1/3 w-64 h-64 bg-gradient-radial from-{{primaryColor}}/5 to-transparent rounded-full blur-2xl animate-float-fast"></div>
      </div>

      <!-- Enhanced Mithila Art Background Pattern -->
      <app-mithila-art-background
        [primaryColor]="primaryColor"
        [secondaryColor]="secondaryColor"
        [opacity]="backgroundOpacity">
      </app-mithila-art-background>

      <!-- Multiple Decorative Borders with Animation -->
      <app-mithila-border
        [primaryColor]="primaryColor"
        [secondaryColor]="secondaryColor"
        [type]="'full'"
        position="top-8 left-8 right-8 bottom-8 animate-border-glow">
      </app-mithila-border>

      <!-- Inner Decorative Border -->
      <div class="absolute top-16 left-16 right-16 bottom-16 border border-white/20 rounded-lg pointer-events-none opacity-60 animate-pulse-slow"></div>

      <!-- Enhanced Floating Decorative Elements -->
      <div *ngIf="showDecorativeElements" class="absolute inset-0 pointer-events-none">
        <!-- Corner Elements -->
        <app-mithila-decorative-element
          [primaryColor]="primaryColor"
          [secondaryColor]="secondaryColor"
          [type]="'lotus'"
          position="absolute top-12 right-12 md:top-20 md:right-20"
          classes="opacity-20 md:opacity-30 animate-float-slow hover:opacity-50 transition-opacity duration-500"
          size="60px md:80px">
        </app-mithila-decorative-element>

        <app-mithila-decorative-element
          [primaryColor]="secondaryColor"
          [secondaryColor]="primaryColor"
          [type]="'peacock'"
          position="absolute bottom-12 left-12 md:bottom-20 md:left-20"
          classes="opacity-20 md:opacity-30 animate-float-medium hover:opacity-50 transition-opacity duration-500"
          size="50px md:60px">
        </app-mithila-decorative-element>

        <app-mithila-decorative-element
          [primaryColor]="primaryColor"
          [secondaryColor]="secondaryColor"
          [type]="'geometric'"
          position="absolute top-1/3 left-1/4"
          classes="opacity-15 md:opacity-20 animate-pulse-slow hover:opacity-40 transition-opacity duration-500"
          size="30px md:40px">
        </app-mithila-decorative-element>

        <!-- Additional Floating Elements -->
        <app-mithila-decorative-element
          [primaryColor]="secondaryColor"
          [secondaryColor]="primaryColor"
          [type]="'fish'"
          position="absolute top-1/4 right-1/3"
          classes="opacity-10 md:opacity-15 animate-float-fast hover:opacity-30 transition-opacity duration-500"
          size="25px md:35px">
        </app-mithila-decorative-element>

        <app-mithila-decorative-element
          [primaryColor]="primaryColor"
          [secondaryColor]="secondaryColor"
          [type]="'elephant'"
          position="absolute bottom-1/3 right-1/4"
          classes="opacity-10 md:opacity-15 animate-float-slow hover:opacity-30 transition-opacity duration-500"
          size="35px md:45px">
        </app-mithila-decorative-element>

        <!-- Scattered Small Elements -->
        <div class="absolute top-1/6 left-1/6 w-3 h-3 bg-{{primaryColor}}/20 rounded-full animate-ping-slow"></div>
        <div class="absolute top-2/3 left-1/5 w-2 h-2 bg-{{secondaryColor}}/30 rounded-full animate-ping-slow delay-1000"></div>
        <div class="absolute bottom-1/6 right-1/6 w-4 h-4 bg-{{primaryColor}}/15 rounded-full animate-ping-slow delay-2000"></div>
        <div class="absolute top-1/2 left-1/12 w-2 h-2 bg-{{secondaryColor}}/25 rounded-full animate-ping-slow delay-3000"></div>
        <div class="absolute bottom-1/4 left-2/3 w-3 h-3 bg-{{primaryColor}}/20 rounded-full animate-ping-slow delay-4000"></div>
      </div>

      <!-- Floating Geometric Shapes -->
      <div *ngIf="showDecorativeElements" class="absolute inset-0 pointer-events-none opacity-10">
        <!-- Floating Circles -->
        <div class="absolute top-1/5 right-1/5 w-32 h-32 border border-white/30 rounded-full animate-float-slow"></div>
        <div class="absolute bottom-1/5 left-1/5 w-24 h-24 border border-white/20 rounded-full animate-float-medium"></div>
        <div class="absolute top-2/3 right-2/3 w-16 h-16 border border-white/25 rounded-full animate-float-fast"></div>

        <!-- Floating Lines -->
        <div class="absolute top-1/3 left-0 w-20 h-px bg-gradient-to-r from-transparent via-white/30 to-transparent animate-float-slow"></div>
        <div class="absolute bottom-1/3 right-0 w-24 h-px bg-gradient-to-l from-transparent via-white/20 to-transparent animate-float-medium"></div>
      </div>

      <!-- Content Container with Enhanced Styling -->
      <div class="container relative z-10 backdrop-blur-[0.5px]">
        <ng-content></ng-content>
      </div>

      <!-- Overlay Glow Effect -->
      <div class="absolute inset-0 bg-gradient-to-t from-transparent via-transparent to-white/5 pointer-events-none"></div>
    </section>
  `,
  styles: [`
    .mithila-floating-section {
      position: relative;
      isolation: isolate;
    }

    /* Enhanced Animation Keyframes */
    @keyframes gradient-background {
      0%, 100% {
        background-position: 0% 50%;
        opacity: 0.7;
      }
      50% {
        background-position: 100% 50%;
        opacity: 0.9;
      }
    }

    @keyframes gradient-shift {
      0%, 100% {
        background-position: 0% 0%;
        opacity: 0.3;
      }
      50% {
        background-position: 100% 100%;
        opacity: 0.6;
      }
    }

    @keyframes float-slow {
      0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
      }
      50% {
        transform: translateY(-15px) rotate(2deg);
        opacity: 1;
      }
    }

    @keyframes float-medium {
      0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.6;
      }
      50% {
        transform: translateY(-10px) rotate(-1deg);
        opacity: 0.9;
      }
    }

    @keyframes float-fast {
      0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.5;
      }
      50% {
        transform: translateY(-8px) rotate(1deg);
        opacity: 0.8;
      }
    }

    @keyframes pulse-slow {
      0%, 100% {
        opacity: 0.2;
        transform: scale(1);
      }
      50% {
        opacity: 0.6;
        transform: scale(1.05);
      }
    }

    @keyframes ping-slow {
      0% {
        transform: scale(1);
        opacity: 1;
      }
      75%, 100% {
        transform: scale(2);
        opacity: 0;
      }
    }

    @keyframes border-glow {
      0%, 100% {
        opacity: 0.5;
        filter: drop-shadow(0 0 5px rgba(255,255,255,0.3));
      }
      50% {
        opacity: 0.8;
        filter: drop-shadow(0 0 15px rgba(255,255,255,0.5));
      }
    }

    /* Apply Animations */
    .animate-gradient-background {
      animation: gradient-background 8s ease-in-out infinite;
      background-size: 200% 200%;
    }

    .animate-gradient-shift {
      animation: gradient-shift 12s ease-in-out infinite;
      background-size: 200% 200%;
    }

    .animate-float-slow {
      animation: float-slow 6s ease-in-out infinite;
    }

    .animate-float-medium {
      animation: float-medium 4s ease-in-out infinite;
    }

    .animate-float-fast {
      animation: float-fast 3s ease-in-out infinite;
    }

    .animate-pulse-slow {
      animation: pulse-slow 4s ease-in-out infinite;
    }

    .animate-ping-slow {
      animation: ping-slow 3s cubic-bezier(0, 0, 0.2, 1) infinite;
    }

    .animate-border-glow {
      animation: border-glow 5s ease-in-out infinite;
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
      .mithila-floating-section .absolute {
        transform: scale(0.7);
      }

      .animate-float-slow,
      .animate-float-medium,
      .animate-float-fast {
        animation-duration: 4s;
      }
    }

    /* Gradient Utilities */
    .bg-gradient-radial {
      background: radial-gradient(circle, var(--tw-gradient-stops));
    }

    /* Hover Effects */
    .mithila-floating-section:hover .animate-float-slow {
      animation-duration: 4s;
    }

    .mithila-floating-section:hover .animate-float-medium {
      animation-duration: 3s;
    }

    .mithila-floating-section:hover .animate-float-fast {
      animation-duration: 2s;
    }
  `]
})
export class MithilaSectionComponent {
  @Input() primaryColor: string = '#C1440E'; // Terracotta Red
  @Input() secondaryColor: string = '#F4B400'; // Sunflower Yellow
  @Input() backgroundGradient: string = 'from-primary-50 via-background-light to-secondary-50';
  @Input() backgroundOpacity: string = '10';
  @Input() padding: string = 'py-16 sm:py-20 md:py-24';
  @Input() classes: string = '';
  @Input() showDecorativeElements: boolean = true;
}
