import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { HeroBannerComponent } from '../../components/shared/hero-banner/hero-banner.component';
import { SectionTitleComponent } from '../../components/shared/section-title/section-title.component';
import { MithilaSectionComponent } from '../../components/shared/mithila-section/mithila-section.component';
import { MithilaArtBackgroundComponent } from '../../components/shared/mithila-art-background/mithila-art-background.component';
import { MithilaBorderComponent } from '../../components/shared/mithila-border/mithila-border.component';
import { MithilaDecorativeElementComponent } from '../../components/shared/mithila-decorative-element/mithila-decorative-element.component';
import { trigger, transition, style, animate, query, stagger } from '@angular/animations';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-blog',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    FormsModule,
    SectionTitleComponent,
    MithilaSectionComponent,
    MithilaArtBackgroundComponent,
    MithilaBorderComponent,
    MithilaDecorativeElementComponent
  ],
  templateUrl: './blog.component.html',
  styleUrl: './blog.component.css',
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('600ms ease-in', style({ opacity: 1 })),
      ]),
    ]),
    trigger('slideIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(30px)' }),
        animate('500ms ease-out', style({ opacity: 1, transform: 'translateY(0)' })),
      ]),
    ]),
    trigger('staggerIn', [
      transition('* => *', [
        query(':enter', [
          style({ opacity: 0, transform: 'translateY(20px)' }),
          stagger('200ms', [
            animate('400ms ease-out', style({ opacity: 1, transform: 'translateY(0)' })),
          ]),
        ], { optional: true }),
      ]),
    ]),
  ]
})
export class BlogComponent {
  // Blog categories
  categories = [
    {
      id: 'art-techniques',
      name: 'Art Techniques',
      description: 'Explore the traditional and contemporary techniques used in Mithila art.',
      image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      color: '#C1440E',
      count: 5
    },
    {
      id: 'cultural-insights',
      name: 'Cultural Insights',
      description: 'Discover the rich cultural heritage and traditions behind Mithila art.',
      image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      color: '#F4B400',
      count: 7
    },
    {
      id: 'artist-stories',
      name: 'Artist Stories',
      description: 'Meet the talented artists who keep the tradition of Mithila art alive.',
      image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
      color: '#3B945E',
      count: 4
    },
    {
      id: 'events-news',
      name: 'Events & News',
      description: 'Stay updated with the latest events, exhibitions, and news from Mithilani Ghar.',
      image: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
      color: '#264653',
      count: 6
    }
  ];

  // Featured blog posts
  featuredPosts = [
    {
      id: 1,
      title: 'The Ancient Symbolism in Mithila Art',
      excerpt: 'Explore the rich symbolism and meaning behind traditional motifs in Mithila painting and how they connect to cultural narratives.',
      content: 'Mithila art is rich with symbolism that has been passed down through generations. Each motif and pattern carries deep cultural significance...',
      image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      author: 'Sarita Devi',
      authorImage: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      date: 'June 15, 2024',
      category: 'cultural-insights',
      tags: ['symbolism', 'tradition', 'culture'],
      readTime: '8 min read',
      featured: true
    },
    {
      id: 2,
      title: 'Modern Innovations in Traditional Mithila Painting',
      excerpt: 'How contemporary artists are bringing fresh perspectives to Mithila art while honoring its traditional roots and techniques.',
      content: 'While respecting the ancient traditions of Mithila art, a new generation of artists is bringing innovation and contemporary themes...',
      image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      author: 'Ramesh Kumar',
      authorImage: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      date: 'May 28, 2024',
      category: 'art-techniques',
      tags: ['innovation', 'contemporary', 'techniques'],
      readTime: '6 min read',
      featured: true
    },
    {
      id: 3,
      title: 'The Journey of Anita Jha: Master Mithila Artist',
      excerpt: 'An inspiring profile of Anita Jha, whose dedication to Mithila art has earned her international recognition and acclaim.',
      content: 'Anita Jha began painting at the age of seven, learning the traditional techniques from her grandmother. Today, her work is displayed in galleries around the world...',
      image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
      author: 'Priya Singh',
      authorImage: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
      date: 'April 10, 2024',
      category: 'artist-stories',
      tags: ['artist profile', 'inspiration', 'success story'],
      readTime: '10 min read',
      featured: true
    }
  ];

  // Recent blog posts
  recentPosts = [
    {
      id: 4,
      title: 'Upcoming Exhibition: Colors of Mithila',
      excerpt: 'Join us for a special exhibition showcasing the vibrant palette and diverse themes in contemporary Mithila art.',
      content: 'We are excited to announce our upcoming exhibition "Colors of Mithila" featuring works from both established masters and emerging talents...',
      image: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
      author: 'Ramesh Kumar',
      authorImage: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      date: 'June 5, 2024',
      category: 'events-news',
      tags: ['exhibition', 'event', 'contemporary art'],
      readTime: '4 min read',
      featured: false
    },
    {
      id: 5,
      title: 'Natural Pigments in Traditional Mithila Art',
      excerpt: 'Learn about the natural materials and traditional methods used to create the vibrant colors in Mithila paintings.',
      content: 'Before modern paints became available, Mithila artists created their pigments from natural sources. This article explores these traditional methods...',
      image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      author: 'Sarita Devi',
      authorImage: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      date: 'May 20, 2024',
      category: 'art-techniques',
      tags: ['natural pigments', 'traditional methods', 'colors'],
      readTime: '7 min read',
      featured: false
    },
    {
      id: 6,
      title: 'Mithila Art in Modern Home Decor',
      excerpt: 'Discover creative ways to incorporate Mithila art into contemporary interior design and home decoration.',
      content: 'The distinctive style of Mithila art can add a unique cultural touch to modern homes. Here are some inspiring ideas for incorporating this traditional art form...',
      image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      author: 'Anita Jha',
      authorImage: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
      date: 'May 12, 2024',
      category: 'cultural-insights',
      tags: ['home decor', 'interior design', 'contemporary living'],
      readTime: '5 min read',
      featured: false
    },
    {
      id: 7,
      title: 'Workshop Recap: Learning Kohbar Art',
      excerpt: 'Highlights from our recent workshop on Kohbar, the traditional marriage chamber art form in Mithila painting.',
      content: 'Last weekend, we hosted a special workshop focused on Kohbar art, the elaborate and symbolic paintings created for the marriage chamber...',
      image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
      author: 'Priya Singh',
      authorImage: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
      date: 'May 5, 2024',
      category: 'events-news',
      tags: ['workshop', 'kohbar art', 'learning'],
      readTime: '6 min read',
      featured: false
    },
    {
      id: 8,
      title: 'The Role of Women in Preserving Mithila Art',
      excerpt: 'Exploring the crucial role women have played in maintaining and evolving the tradition of Mithila painting through generations.',
      content: 'Historically, Mithila art was practiced exclusively by women who passed down techniques and motifs from mother to daughter...',
      image: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
      author: 'Sarita Devi',
      authorImage: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      date: 'April 25, 2024',
      category: 'cultural-insights',
      tags: ['women artists', 'cultural preservation', 'heritage'],
      readTime: '9 min read',
      featured: false
    },
    {
      id: 9,
      title: 'Interview with Sunil Yadav: Bringing Mithila Art to Digital Media',
      excerpt: 'Meet Sunil Yadav, an innovative artist who is adapting traditional Mithila techniques for digital creation and global audiences.',
      content: 'Sunil Yadav represents a new generation of Mithila artists who are embracing technology while honoring tradition...',
      image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      author: 'Ramesh Kumar',
      authorImage: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      date: 'April 18, 2024',
      category: 'artist-stories',
      tags: ['digital art', 'innovation', 'interview'],
      readTime: '8 min read',
      featured: false
    }
  ];

  // Popular tags
  popularTags = [
    { name: 'tradition', count: 12 },
    { name: 'techniques', count: 9 },
    { name: 'symbolism', count: 8 },
    { name: 'contemporary', count: 7 },
    { name: 'exhibitions', count: 6 },
    { name: 'artists', count: 6 },
    { name: 'workshops', count: 5 },
    { name: 'cultural heritage', count: 5 },
    { name: 'innovation', count: 4 },
    { name: 'history', count: 4 }
  ];

  // Filter functionality
  activeCategory: string = 'all';
  searchQuery: string = '';

  setActiveCategory(category: string) {
    this.activeCategory = category;
  }

  get filteredPosts() {
    const allPosts = [...this.featuredPosts, ...this.recentPosts];

    // First filter by category
    const categoryFiltered = this.activeCategory === 'all'
      ? allPosts
      : allPosts.filter(post => post.category === this.activeCategory);

    // Then filter by search query if present
    if (!this.searchQuery.trim()) {
      return categoryFiltered;
    }

    const query = this.searchQuery.toLowerCase().trim();
    return categoryFiltered.filter(post =>
      post.title.toLowerCase().includes(query) ||
      post.excerpt.toLowerCase().includes(query) ||
      post.tags.some(tag => tag.toLowerCase().includes(query))
    );
  }

  // Get all posts for display
  get allPosts() {
    return [...this.featuredPosts, ...this.recentPosts];
  }
}
