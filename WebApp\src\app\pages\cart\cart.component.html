<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
  <!-- Enhanced Header -->
  <div class="bg-white shadow-sm border-b">
    <div class="container mx-auto px-4 py-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Shopping Cart</h1>
          <p class="text-gray-600 mt-1">{{getCartItemCount()}} item(s) in your cart</p>
        </div>
        <div class="hidden md:block">
          <div class="flex items-center space-x-2 text-primary-600">
            <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"/>
            </svg>
            <span class="text-2xl font-bold">{{getCartItemCount()}}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Cart Content -->
  <div class="container mx-auto px-4 py-8">
    <!-- Enhanced Empty Cart -->
    <div *ngIf="cartItems.length === 0 && !orderSubmitted" class="text-center py-16">
      <div class="max-w-md mx-auto">
        <div class="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
          <svg class="h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"/>
          </svg>
        </div>
        <h2 class="text-2xl font-bold text-gray-900 mb-4">Your cart is empty</h2>
        <p class="text-gray-600 mb-8">Discover our beautiful collection of authentic Mithila art pieces.</p>
        <a routerLink="/products"
           class="inline-block bg-gradient-to-r from-primary-600 to-primary-700 text-white px-8 py-3 rounded-lg hover:from-primary-700 hover:to-primary-800 transition-all duration-300 transform hover:scale-105 shadow-lg font-medium">
          Start Shopping
        </a>
      </div>
    </div>

    <!-- Enhanced Cart Items -->
    <div *ngIf="cartItems.length > 0 && !showCheckoutForm && !orderSubmitted" class="max-w-4xl mx-auto">
      <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
        <div class="p-6 border-b bg-gradient-to-r from-primary-50 to-secondary-50">
          <h2 class="text-xl font-bold text-gray-900">Cart Items</h2>
        </div>

        <div class="divide-y divide-gray-200">
          <div *ngFor="let item of cartItems; let i = index"
               class="p-6 hover:bg-gray-50 transition-colors duration-300 animate-fade-in-up"
               [style.animation-delay]="(i * 100) + 'ms'">
            <div class="flex items-center space-x-6">
              <!-- Product Image -->
              <div class="flex-shrink-0">
                <img [src]="item.image" [alt]="item.name"
                     class="h-24 w-24 object-cover rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
              </div>

              <!-- Product Details -->
              <div class="flex-1 min-w-0">
                <h3 class="text-lg font-semibold text-gray-900 mb-1">{{item.name}}</h3>
                <p class="text-sm text-gray-600 mb-2">By {{item.artist}}</p>
                <p class="text-lg font-bold text-primary-600">{{formatPrice(item.price)}}</p>
              </div>

              <!-- Quantity Controls -->
              <div class="flex items-center space-x-3">
                <button
                  (click)="updateQuantity(item.id, item.quantity - 1)"
                  class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 hover:border-primary-300 transition-all duration-300">
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"/>
                  </svg>
                </button>
                <span class="w-8 text-center font-semibold">{{item.quantity}}</span>
                <button
                  (click)="updateQuantity(item.id, item.quantity + 1)"
                  class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 hover:border-primary-300 transition-all duration-300">
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                  </svg>
                </button>
              </div>

              <!-- Item Total -->
              <div class="text-right">
                <p class="text-lg font-bold text-gray-900">{{formatPrice(item.price * item.quantity)}}</p>
              </div>

              <!-- Remove Button -->
              <button
                (click)="removeItem(item.id)"
                class="text-red-500 hover:text-red-700 transition-colors duration-300 p-2 rounded-full hover:bg-red-50">
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Enhanced Cart Summary -->
        <div class="bg-gradient-to-r from-primary-50 to-secondary-50 p-6">
          <div class="flex justify-between items-center mb-6">
            <span class="text-lg font-semibold text-gray-900">Total:</span>
            <span class="text-2xl font-bold text-primary-600">{{formatPrice(getCartTotal())}}</span>
          </div>

          <div class="flex gap-4">
            <a routerLink="/products"
               class="flex-1 px-6 py-3 border-2 border-primary-600 text-primary-600 rounded-lg font-medium text-center hover:bg-primary-50 transition-all duration-300 transform hover:scale-105">
              Continue Shopping
            </a>
            <button
              (click)="proceedToCheckout()"
              class="flex-1 px-6 py-3 bg-gradient-to-r from-primary-600 to-primary-700 text-white rounded-lg font-medium hover:from-primary-700 hover:to-primary-800 transition-all duration-300 transform hover:scale-105 shadow-lg">
              Proceed to Checkout
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Checkout Form -->
    <div *ngIf="showCheckoutForm" id="checkout-form" class="max-w-2xl mx-auto">
      <div class="bg-white rounded-2xl shadow-lg p-8">
        <div class="text-center mb-8">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">Checkout</h2>
          <p class="text-gray-600">Please fill out your details to complete your order.</p>
        </div>

        <form [formGroup]="checkoutForm" (ngSubmit)="onSubmitOrder()" class="space-y-6">
          <!-- Customer Information -->
          <div class="bg-gray-50 p-6 rounded-xl">
            <h3 class="text-xl font-bold text-gray-900 mb-4">Customer Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                <input 
                  type="text" 
                  formControlName="customerName"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <div *ngIf="getFieldError('customerName')" class="text-red-500 text-sm mt-1">
                  {{getFieldError('customerName')}}
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                <input 
                  type="email" 
                  formControlName="email"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <div *ngIf="getFieldError('email')" class="text-red-500 text-sm mt-1">
                  {{getFieldError('email')}}
                </div>
              </div>
              
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                <input 
                  type="tel" 
                  formControlName="phone"
                  placeholder="9876543210"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <div *ngIf="getFieldError('phone')" class="text-red-500 text-sm mt-1">
                  {{getFieldError('phone')}}
                </div>
              </div>
            </div>
          </div>

          <!-- Shipping Address -->
          <div class="bg-gray-50 p-6 rounded-xl">
            <h3 class="text-xl font-bold text-gray-900 mb-4">Shipping Address</h3>
            
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Address *</label>
                <textarea 
                  formControlName="address"
                  rows="3"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"></textarea>
                <div *ngIf="getFieldError('address')" class="text-red-500 text-sm mt-1">
                  {{getFieldError('address')}}
                </div>
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">City *</label>
                  <input 
                    type="text" 
                    formControlName="city"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                  <div *ngIf="getFieldError('city')" class="text-red-500 text-sm mt-1">
                    {{getFieldError('city')}}
                  </div>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Postal Code *</label>
                  <input 
                    type="text" 
                    formControlName="postalCode"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                  <div *ngIf="getFieldError('postalCode')" class="text-red-500 text-sm mt-1">
                    {{getFieldError('postalCode')}}
                  </div>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Country *</label>
                  <input 
                    type="text" 
                    formControlName="country"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                  <div *ngIf="getFieldError('country')" class="text-red-500 text-sm mt-1">
                    {{getFieldError('country')}}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Special Requests -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Special Requests (Optional)</label>
            <textarea 
              formControlName="specialRequests"
              rows="3"
              placeholder="Any special requirements or customizations..."
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"></textarea>
          </div>

          <!-- Order Summary -->
          <div class="bg-primary-50 p-6 rounded-xl">
            <h3 class="text-xl font-bold text-gray-900 mb-4">Order Summary</h3>
            <div class="space-y-2">
              <div *ngFor="let item of cartItems" class="flex justify-between">
                <span>{{item.name}} (x{{item.quantity}})</span>
                <span class="font-medium">{{formatPrice(item.price * item.quantity)}}</span>
              </div>
              <hr class="my-2">
              <div class="flex justify-between text-lg font-bold">
                <span>Total:</span>
                <span class="text-primary-600">{{formatPrice(getCartTotal())}}</span>
              </div>
            </div>
          </div>

          <!-- Terms and Submit -->
          <div class="space-y-4">
            <div class="flex items-start">
              <input 
                type="checkbox" 
                formControlName="agreeToTerms"
                class="mt-1 mr-3 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
              <label class="text-sm text-gray-700">
                I agree to the terms and conditions and understand that this is an order request. 
                Payment details will be discussed upon confirmation. *
              </label>
            </div>
            <div *ngIf="getFieldError('agreeToTerms')" class="text-red-500 text-sm">
              You must agree to the terms and conditions
            </div>
            
            <div class="flex gap-4">
              <button 
                type="button"
                (click)="continueShopping()"
                class="flex-1 px-6 py-4 border-2 border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-colors duration-300">
                Back to Cart
              </button>
              <button 
                type="submit"
                [disabled]="!checkoutForm.valid"
                class="flex-1 bg-primary-600 text-white px-8 py-4 rounded-xl font-bold hover:bg-primary-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-300">
                Submit Order Request
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Order Success Message -->
    <div *ngIf="orderSubmitted" id="order-success" class="max-w-2xl mx-auto">
      <div class="bg-white rounded-2xl shadow-lg p-8 text-center">
        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
          </svg>
        </div>
        <h2 class="text-2xl font-bold text-gray-900 mb-4">Order Request Submitted!</h2>
        <p class="text-gray-600 mb-6">
          Thank you for your order request. We have received your details and will contact you within 24 hours to confirm the order and discuss payment options.
        </p>
        <div class="flex gap-4 justify-center">
          <a routerLink="/products" 
             class="bg-primary-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-primary-700 transition-colors duration-300">
            Continue Shopping
          </a>
          <a routerLink="/contact" 
             class="border-2 border-primary-600 text-primary-600 px-6 py-3 rounded-lg font-medium hover:bg-primary-50 transition-colors duration-300">
            Contact Us
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
