from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from django.shortcuts import get_object_or_404
from django.db.models import Q
from .models import (
    SocialMediaLink, Artist, ArtistSpotlight, TeamMember, Category, Product,
    GalleryImage, FAQ, Testimonial, CompanyFeature, CompanyInfo, Achievement, Facility,
    Cart, CartItem, Order, OrderItem, ContactInquiry, ArtistApplication
)
from .serializers import (
    SocialMediaLinkSerializer, ArtistSerializer, ArtistSpotlightSerializer,
    TeamMemberSerializer, CategorySerializer, ProductSerializer, GalleryImageSerializer,
    FAQSerializer, TestimonialSerializer, CompanyFeatureSerializer, CompanyInfoSerializer,
    AchievementSerializer, FacilitySerializer, CartSerializer, CartItemSerializer,
    OrderSerializer, OrderItemSerializer, ContactInquirySerializer, ArtistApplicationSerializer,
    AddToCartSerializer, UpdateCartItemSerializer, CreateOrderSerializer
)


class SocialMediaLinkViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = SocialMediaLink.objects.all()
    serializer_class = SocialMediaLinkSerializer
    permission_classes = [AllowAny]


class ArtistViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Artist.objects.all()
    serializer_class = ArtistSerializer
    permission_classes = [AllowAny]

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Get featured artists (first 3)"""
        featured_artists = self.queryset[:3]
        serializer = self.get_serializer(featured_artists, many=True)
        return Response(serializer.data)


class ArtistSpotlightViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = ArtistSpotlight.objects.all()
    serializer_class = ArtistSpotlightSerializer
    permission_classes = [AllowAny]


class TeamMemberViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = TeamMember.objects.all()
    serializer_class = TeamMemberSerializer
    permission_classes = [AllowAny]


class CategoryViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Category.objects.all()
    serializer_class = CategorySerializer
    permission_classes = [AllowAny]


class ProductViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Product.objects.all()
    serializer_class = ProductSerializer
    permission_classes = [AllowAny]
    lookup_field = 'slug'

    def get_queryset(self):
        queryset = Product.objects.all()
        category = self.request.query_params.get('category', None)
        featured = self.request.query_params.get('featured', None)
        search = self.request.query_params.get('search', None)

        if category and category != 'All':
            queryset = queryset.filter(category__name=category)

        if featured == 'true':
            queryset = queryset.filter(featured=True)

        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search) |
                Q(artists__artist__name__icontains=search)
            ).distinct()

        return queryset

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Get featured products"""
        featured_products = self.queryset.filter(featured=True)
        serializer = self.get_serializer(featured_products, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def categories(self, request):
        """Get all product categories"""
        categories = Category.objects.all()
        category_names = ['All'] + [cat.name for cat in categories]
        return Response(category_names)


class GalleryImageViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = GalleryImage.objects.all()
    serializer_class = GalleryImageSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        queryset = GalleryImage.objects.all()
        category = self.request.query_params.get('category', None)

        if category and category != 'All':
            queryset = queryset.filter(category=category)

        return queryset

    @action(detail=False, methods=['get'])
    def categories(self, request):
        """Get all gallery categories"""
        categories = GalleryImage.objects.values_list('category', flat=True).distinct()
        category_list = ['All'] + list(categories)
        return Response(category_list)


class FAQViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = FAQ.objects.filter(is_active=True)
    serializer_class = FAQSerializer
    permission_classes = [AllowAny]


# ContactInfoViewSet removed - consolidated into CompanyInfoViewSet


class TestimonialViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Testimonial.objects.filter(is_active=True)
    serializer_class = TestimonialSerializer
    permission_classes = [AllowAny]


# EventViewSet removed - not implemented in frontend


class CompanyFeatureViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = CompanyFeature.objects.all()
    serializer_class = CompanyFeatureSerializer
    permission_classes = [AllowAny]


class CompanyInfoViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = CompanyInfo.objects.all()
    serializer_class = CompanyInfoSerializer
    permission_classes = [AllowAny]

    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get current company info (singleton)"""
        company_info = CompanyInfo.objects.first()
        if company_info:
            # Get features separately
            features = CompanyFeature.objects.all()
            serializer = self.get_serializer(company_info)
            data = serializer.data
            data['features'] = CompanyFeatureSerializer(features, many=True).data
            return Response(data)
        return Response({})


# AboutUsStoryViewSet and AboutUsMissionViewSet removed - consolidated into CompanyInfoViewSet


class AchievementViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Achievement.objects.all()
    serializer_class = AchievementSerializer
    permission_classes = [AllowAny]


class FacilityViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Facility.objects.all()
    serializer_class = FacilitySerializer
    permission_classes = [AllowAny]


class CartViewSet(viewsets.ModelViewSet):
    queryset = Cart.objects.all()
    serializer_class = CartSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        session_key = self.request.session.session_key
        if not session_key:
            self.request.session.create()
            session_key = self.request.session.session_key

        if self.request.user.is_authenticated:
            return Cart.objects.filter(user=self.request.user)
        else:
            return Cart.objects.filter(session_key=session_key)

    def get_or_create_cart(self):
        session_key = self.request.session.session_key
        if not session_key:
            self.request.session.create()
            session_key = self.request.session.session_key

        if self.request.user.is_authenticated:
            cart, created = Cart.objects.get_or_create(user=self.request.user)
        else:
            cart, created = Cart.objects.get_or_create(session_key=session_key)

        return cart

    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get current user's cart"""
        cart = self.get_or_create_cart()
        serializer = self.get_serializer(cart)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def add_item(self, request):
        """Add item to cart"""
        serializer = AddToCartSerializer(data=request.data)
        if serializer.is_valid():
            cart = self.get_or_create_cart()
            product_id = serializer.validated_data['product_id']
            quantity = serializer.validated_data['quantity']

            try:
                product = Product.objects.get(id=product_id)
                cart_item, created = CartItem.objects.get_or_create(
                    cart=cart,
                    product=product,
                    defaults={'quantity': quantity}
                )

                if not created:
                    cart_item.quantity += quantity
                    cart_item.save()

                cart_serializer = self.get_serializer(cart)
                return Response(cart_serializer.data, status=status.HTTP_201_CREATED)

            except Product.DoesNotExist:
                return Response(
                    {'error': 'Product not found'},
                    status=status.HTTP_404_NOT_FOUND
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['put'])
    def update_item(self, request):
        """Update cart item quantity"""
        item_id = request.data.get('item_id')
        serializer = UpdateCartItemSerializer(data=request.data)

        if serializer.is_valid():
            try:
                cart = self.get_or_create_cart()
                cart_item = CartItem.objects.get(id=item_id, cart=cart)
                cart_item.quantity = serializer.validated_data['quantity']
                cart_item.save()

                cart_serializer = self.get_serializer(cart)
                return Response(cart_serializer.data)

            except CartItem.DoesNotExist:
                return Response(
                    {'error': 'Cart item not found'},
                    status=status.HTTP_404_NOT_FOUND
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['delete'])
    def remove_item(self, request):
        """Remove item from cart"""
        item_id = request.data.get('item_id')

        try:
            cart = self.get_or_create_cart()
            cart_item = CartItem.objects.get(id=item_id, cart=cart)
            cart_item.delete()

            cart_serializer = self.get_serializer(cart)
            return Response(cart_serializer.data)

        except CartItem.DoesNotExist:
            return Response(
                {'error': 'Cart item not found'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['delete'])
    def clear(self, request):
        """Clear all items from cart"""
        cart = self.get_or_create_cart()
        cart.items.all().delete()

        cart_serializer = self.get_serializer(cart)
        return Response(cart_serializer.data)


class OrderViewSet(viewsets.ModelViewSet):
    queryset = Order.objects.all()
    serializer_class = OrderSerializer
    permission_classes = [AllowAny]

    def create(self, request):
        """Create order from cart"""
        serializer = CreateOrderSerializer(data=request.data)
        if serializer.is_valid():
            # Get cart
            session_key = request.session.session_key
            if not session_key:
                request.session.create()
                session_key = request.session.session_key

            if request.user.is_authenticated:
                cart = Cart.objects.filter(user=request.user).first()
            else:
                cart = Cart.objects.filter(session_key=session_key).first()

            if not cart or not cart.items.exists():
                return Response(
                    {'error': 'Cart is empty'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Calculate total
            total_amount = sum(item.total_price for item in cart.items.all())

            # Create order
            order = serializer.save(total_amount=total_amount)

            # Create order items
            for cart_item in cart.items.all():
                OrderItem.objects.create(
                    order=order,
                    product=cart_item.product,
                    quantity=cart_item.quantity,
                    price=cart_item.product.price
                )

            # Clear cart
            cart.items.all().delete()

            order_serializer = OrderSerializer(order)
            return Response(order_serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ContactInquiryViewSet(viewsets.ModelViewSet):
    queryset = ContactInquiry.objects.all()
    serializer_class = ContactInquirySerializer
    permission_classes = [AllowAny]

    def create(self, request):
        """Create contact inquiry"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {'message': 'Contact inquiry submitted successfully'},
                status=status.HTTP_201_CREATED
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ArtistApplicationViewSet(viewsets.ModelViewSet):
    queryset = ArtistApplication.objects.all()
    serializer_class = ArtistApplicationSerializer
    permission_classes = [AllowAny]

    def create(self, request):
        """Create artist application"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {'message': 'Artist application submitted successfully'},
                status=status.HTTP_201_CREATED
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# Additional API endpoints for frontend compatibility
class DataServiceViewSet(viewsets.ViewSet):
    """
    ViewSet to provide endpoints that match the Angular data service structure
    """
    permission_classes = [AllowAny]

    @action(detail=False, methods=['get'])
    def art_styles(self, request):
        """Get art styles"""
        art_styles = [
            'Traditional Madhubani',
            'Contemporary Mithila',
            'Kohbar Art',
            'Bharni Style',
            'Katchni Style',
            'Tantrik Style',
            'Godna Style'
        ]
        return Response(art_styles)

    @action(detail=False, methods=['get'])
    def experience_levels(self, request):
        """Get experience levels"""
        experience_levels = [
            {'value': 'beginner', 'label': 'Beginner (0-2 years)'},
            {'value': 'intermediate', 'label': 'Intermediate (3-5 years)'},
            {'value': 'advanced', 'label': 'Advanced (6-10 years)'},
            {'value': 'expert', 'label': 'Expert (10+ years)'}
        ]
        return Response(experience_levels)

    @action(detail=False, methods=['get'])
    def product_categories(self, request):
        """Get product categories"""
        categories = Category.objects.all()
        category_names = ['All'] + [cat.name for cat in categories]
        return Response(category_names)

    @action(detail=False, methods=['get'])
    def gallery_categories(self, request):
        """Get gallery categories"""
        categories = GalleryImage.objects.values_list('category', flat=True).distinct()
        category_list = ['All'] + list(categories)
        return Response(category_list)

    @action(detail=False, methods=['get'])
    def about_us(self, request):
        """Get complete about us information"""
        company_info = CompanyInfo.objects.first()
        team = TeamMember.objects.all()
        achievements = Achievement.objects.all()
        facilities = Facility.objects.all()

        # Extract story and mission data from company_info
        story_data = {}
        mission_data = {}

        if company_info:
            story_data = {
                'title': company_info.story_title,
                'content': company_info.story_content,
                'image': company_info.story_image
            }
            mission_data = {
                'title': company_info.mission_title,
                'content': company_info.mission_content,
                'values': company_info.values
            }

        data = {
            'story': story_data,
            'mission': mission_data,
            'team': TeamMemberSerializer(team, many=True).data,
            'achievements': AchievementSerializer(achievements, many=True).data,
            'facilities': FacilitySerializer(facilities, many=True).data,
        }

        return Response(data)
